import type { EmailSendRequest, EmailSendResult, EmailServiceConfig } from '@psii/shared-types'

// Base email provider interface
export interface EmailProvider {
  name: string
  send(request: EmailSendRequest, config: EmailServiceConfig): Promise<EmailSendResult>
  validateConfig(config: Partial<EmailServiceConfig>): { valid: boolean; errors: string[] }
}

// Supabase Auth email provider
export class SupabaseEmailProvider implements EmailProvider {
  name = 'supabase'
  
  async send(request: EmailSendRequest, config: EmailServiceConfig): Promise<EmailSendResult> {
    // For development/testing - log instead of sending
    if (process.env.NODE_ENV === 'development') {
      console.log(`📧 [DEV] Email would be sent via Supabase:`)
      console.log(`To: ${request.to}`)
      console.log(`Template: ${request.template_type}`)
      console.log(`Variables:`, request.variables)
      
      return {
        success: true,
        message_id: `dev-${Date.now()}`,
        provider_used: 'supabase'
      }
    }

    try {
      // In production, use Supabase Auth email
      const { createClient } = await import('@supabase/supabase-js')
      const supabase = createClient(
        process.env.PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      )

      if (request.template_type === 'user_invitation') {
        const { error } = await supabase.auth.admin.inviteUserByEmail(request.to, {
          redirectTo: `${process.env.PUBLIC_SITE_URL}/accept-invitation?token=${request.variables.token}`
        })
        
        if (error) throw error
      } else if (request.template_type === 'password_reset') {
        const { error } = await supabase.auth.resetPasswordForEmail(request.to, {
          redirectTo: `${process.env.PUBLIC_SITE_URL}/reset-password`
        })
        
        if (error) throw error
      }

      return {
        success: true,
        message_id: `supabase-${Date.now()}`,
        provider_used: 'supabase'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider_used: 'supabase'
      }
    }
  }

  validateConfig(config: Partial<EmailServiceConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!config.from_email) errors.push('From email is required')
    if (!config.from_name) errors.push('From name is required')
    
    return { valid: errors.length === 0, errors }
  }
}

// ZeptoMail provider
export class ZeptoMailProvider implements EmailProvider {
  name = 'zeptomail'
  
  async send(request: EmailSendRequest, config: EmailServiceConfig): Promise<EmailSendResult> {
    try {
      const response = await fetch('https://api.zeptomail.com/v1.1/email', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Zoho-enczapikey ${config.api_key}`
        },
        body: JSON.stringify({
          from: { address: config.from_email, name: config.from_name },
          to: [{ email_address: { address: request.to } }],
          subject: this.getSubjectForTemplate(request.template_type, request.variables),
          htmlbody: this.getHtmlForTemplate(request.template_type, request.variables),
          textbody: this.getTextForTemplate(request.template_type, request.variables)
        })
      })

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || 'ZeptoMail API error')
      }

      return {
        success: true,
        message_id: result.data?.[0]?.message_id,
        provider_used: 'zeptomail'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider_used: 'zeptomail'
      }
    }
  }

  validateConfig(config: Partial<EmailServiceConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!config.api_key) errors.push('ZeptoMail API key is required')
    if (!config.from_email) errors.push('From email is required')
    if (!config.from_name) errors.push('From name is required')
    
    return { valid: errors.length === 0, errors }
  }

  private getSubjectForTemplate(type: string, variables: Record<string, any>): string {
    const subjects = {
      user_invitation: `Welcome to ${variables.company_name || 'ProcureServe'}`,
      password_reset: 'Reset your password',
      welcome: `Welcome to ${variables.company_name || 'ProcureServe'}`,
      notification: variables.subject || 'Notification'
    }
    return subjects[type] || 'Notification'
  }

  private getHtmlForTemplate(type: string, variables: Record<string, any>): string {
    // Simple templates - can be enhanced later
    const templates = {
      user_invitation: `
        <h2>You've been invited to join ${variables.company_name}</h2>
        <p>Click <a href="${variables.invitation_url}">here</a> to accept your invitation.</p>
      `,
      password_reset: `
        <h2>Reset Your Password</h2>
        <p>Click <a href="${variables.reset_url}">here</a> to reset your password.</p>
      `
    }
    return templates[type] || `<p>${variables.message || 'Notification'}</p>`
  }

  private getTextForTemplate(type: string, variables: Record<string, any>): string {
    const templates = {
      user_invitation: `You've been invited to join ${variables.company_name}. Visit: ${variables.invitation_url}`,
      password_reset: `Reset your password by visiting: ${variables.reset_url}`
    }
    return templates[type] || variables.message || 'Notification'
  }
}

// SMTP provider (for Gmail, custom SMTP, etc.)
export class SMTPProvider implements EmailProvider {
  name = 'smtp'
  
  async send(request: EmailSendRequest, config: EmailServiceConfig): Promise<EmailSendResult> {
    try {
      const nodemailer = await import('nodemailer')
      
      const transporter = nodemailer.createTransporter({
        host: config.smtp_host,
        port: config.smtp_port || 587,
        secure: config.smtp_port === 465,
        auth: {
          user: config.smtp_username,
          pass: config.smtp_password
        }
      })

      const info = await transporter.sendMail({
        from: `"${config.from_name}" <${config.from_email}>`,
        to: request.to,
        subject: this.getSubjectForTemplate(request.template_type, request.variables),
        html: this.getHtmlForTemplate(request.template_type, request.variables),
        text: this.getTextForTemplate(request.template_type, request.variables)
      })

      return {
        success: true,
        message_id: info.messageId,
        provider_used: 'smtp'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider_used: 'smtp'
      }
    }
  }

  validateConfig(config: Partial<EmailServiceConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!config.smtp_host) errors.push('SMTP host is required')
    if (!config.smtp_username) errors.push('SMTP username is required')
    if (!config.smtp_password) errors.push('SMTP password is required')
    if (!config.from_email) errors.push('From email is required')
    
    return { valid: errors.length === 0, errors }
  }

  private getSubjectForTemplate(type: string, variables: Record<string, any>): string {
    // Same as ZeptoMail implementation
    const subjects = {
      user_invitation: `Welcome to ${variables.company_name || 'ProcureServe'}`,
      password_reset: 'Reset your password',
      welcome: `Welcome to ${variables.company_name || 'ProcureServe'}`,
      notification: variables.subject || 'Notification'
    }
    return subjects[type] || 'Notification'
  }

  private getHtmlForTemplate(type: string, variables: Record<string, any>): string {
    // Same as ZeptoMail implementation
    const templates = {
      user_invitation: `
        <h2>You've been invited to join ${variables.company_name}</h2>
        <p>Click <a href="${variables.invitation_url}">here</a> to accept your invitation.</p>
      `,
      password_reset: `
        <h2>Reset Your Password</h2>
        <p>Click <a href="${variables.reset_url}">here</a> to reset your password.</p>
      `
    }
    return templates[type] || `<p>${variables.message || 'Notification'}</p>`
  }

  private getTextForTemplate(type: string, variables: Record<string, any>): string {
    // Same as ZeptoMail implementation
    const templates = {
      user_invitation: `You've been invited to join ${variables.company_name}. Visit: ${variables.invitation_url}`,
      password_reset: `Reset your password by visiting: ${variables.reset_url}`
    }
    return templates[type] || variables.message || 'Notification'
  }
}
