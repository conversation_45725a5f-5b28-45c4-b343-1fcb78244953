import type { EmailSendRequest, EmailSendResult, EmailServiceConfig, EmailTemplateType } from '@psii/shared-types'
import { SupabaseEmailProvider, ZeptoMailProvider, SMTPProvider, type EmailProvider } from './providers'

export class EmailServiceManager {
  private providers: Map<string, EmailProvider> = new Map()
  private supabase: any

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient
    
    // Register available providers
    this.providers.set('supabase', new SupabaseEmailProvider())
    this.providers.set('zeptomail', new ZeptoMailProvider())
    this.providers.set('smtp', new SMTPProvider())
  }

  async sendEmail(request: EmailSendRequest): Promise<EmailSendResult> {
    try {
      // Get active email configuration for the company
      const config = await this.getActiveEmailConfig(request.company_id, request.template_type)
      
      if (!config) {
        // Fallback to Supabase for development
        return await this.sendWithFallback(request)
      }

      const provider = this.providers.get(config.provider_type)
      if (!provider) {
        throw new Error(`Email provider ${config.provider_type} not found`)
      }

      // Send email with the configured provider
      const result = await provider.send(request, config)
      
      // Track usage
      if (result.success) {
        await this.trackEmailUsage(config.id)
      }

      return result
    } catch (error) {
      console.error('Email send error:', error)
      
      // Fallback to Supabase if configured provider fails
      return await this.sendWithFallback(request)
    }
  }

  private async getActiveEmailConfig(
    companyId: string, 
    templateType: EmailTemplateType
  ): Promise<EmailServiceConfig | null> {
    const capabilityField = this.getCapabilityField(templateType)
    
    const { data, error } = await this.supabase
      .from('email_service_config')
      .select('*')
      .eq('company_id', companyId)
      .eq('is_active', true)
      .eq(capabilityField, true)
      .order('is_primary', { ascending: false })
      .limit(1)
      .single()

    if (error || !data) {
      console.log(`No email config found for ${templateType}, using fallback`)
      return null
    }

    return data
  }

  private getCapabilityField(templateType: EmailTemplateType): string {
    const mapping = {
      user_invitation: 'handles_invitations',
      password_reset: 'handles_password_resets',
      welcome: 'handles_notifications',
      notification: 'handles_notifications'
    }
    return mapping[templateType] || 'handles_notifications'
  }

  private async sendWithFallback(request: EmailSendRequest): Promise<EmailSendResult> {
    console.log('Using Supabase fallback for email sending')
    
    const fallbackConfig: EmailServiceConfig = {
      id: 'fallback',
      company_id: request.company_id,
      provider_type: 'supabase',
      is_active: true,
      is_primary: false,
      from_email: '<EMAIL>',
      from_name: 'ProcureServe',
      handles_invitations: true,
      handles_password_resets: true,
      handles_notifications: true,
      daily_email_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const provider = this.providers.get('supabase')!
    return await provider.send(request, fallbackConfig)
  }

  private async trackEmailUsage(configId: string): Promise<void> {
    try {
      await this.supabase
        .from('email_service_config')
        .update({
          last_used_at: new Date().toISOString(),
          daily_email_count: this.supabase.rpc('increment_daily_count', { config_id: configId })
        })
        .eq('id', configId)
    } catch (error) {
      console.error('Failed to track email usage:', error)
    }
  }

  // Validate email configuration
  async validateConfig(config: Partial<EmailServiceConfig>): Promise<{ valid: boolean; errors: string[] }> {
    if (!config.provider_type) {
      return { valid: false, errors: ['Provider type is required'] }
    }

    const provider = this.providers.get(config.provider_type)
    if (!provider) {
      return { valid: false, errors: [`Provider ${config.provider_type} is not supported`] }
    }

    return provider.validateConfig(config)
  }

  // Test email configuration
  async testEmailConfig(configId: string, testEmail: string): Promise<EmailSendResult> {
    const { data: config } = await this.supabase
      .from('email_service_config')
      .select('*')
      .eq('id', configId)
      .single()

    if (!config) {
      return { success: false, error: 'Email configuration not found' }
    }

    const testRequest: EmailSendRequest = {
      to: testEmail,
      template_type: 'notification',
      variables: {
        message: 'This is a test email from your email configuration.',
        subject: 'Email Configuration Test'
      },
      company_id: config.company_id
    }

    const provider = this.providers.get(config.provider_type)!
    return await provider.send(testRequest, config)
  }
}

// Helper function to create email service manager
export function createEmailService(supabaseClient: any): EmailServiceManager {
  return new EmailServiceManager(supabaseClient)
}
