# PSII - Supabase Cloud Configuration Template
# Copy this to .env and update with your credentials

# Supabase Cloud Configuration
PUBLIC_SUPABASE_URL=https://your-project.supabase.co
PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Database Configuration (Cloud)
DATABASE_URL=postgresql://postgres.your-project:<EMAIL>:5432/postgres

# Application Ports
CUSTOMER_APP_PORT=3004
CONSOLE_APP_PORT=3008  
CANDIDATE_APP_PORT=3006

# Development URLs  
CUSTOMER_APP_URL=http://localhost:3004
CONSOLE_APP_URL=http://localhost:3008
CANDIDATE_APP_URL=http://localhost:3006

# Cloud Environment Settings
NODE_ENV=development
SUPABASE_ENVIRONMENT=cloud
SKIP_LOCAL_SUPABASE=true

# Optional: File upload settings
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Optional: OpenAI API Key for AI features
# OPENAI_API_KEY=your_openai_api_key_here

# Optional: Email service configuration (Supabase provides this in cloud)
# RESEND_API_KEY=your_resend_key_here
# ZEPTO_API_KEY=your_zepto_key_here
