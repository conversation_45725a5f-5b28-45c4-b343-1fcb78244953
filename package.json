{"name": "procureserve-ii", "version": "1.0.0", "type": "module", "description": "ProcureServe II - Enterprise Staffing Platform", "scripts": {"dev": "npm run dev:customer", "dev:customer": "cd apps/customer-app && npm run dev", "dev:candidate": "cd apps/candidate-app && npm run dev", "dev:console": "cd apps/console-app && npm run dev", "dev:supabase": "npx supabase start", "build": "npm run build:customer && npm run build:console", "build:customer": "cd apps/customer-app && npm run build", "build:candidate": "cd apps/candidate-app && npm run build", "build:console": "cd apps/console-app && npm run build", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:reset": "npx supabase db reset", "supabase:status": "npx supabase status", "setup": "npm run supabase:start"}, "dependencies": {"@supabase/supabase-js": "^2.39.0"}}