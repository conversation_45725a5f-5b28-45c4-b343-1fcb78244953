#!/usr/bin/env node

/**
 * Universal Docker Manager for PSII
 * Fixes Docker daemon connectivity issues and integrates with port management
 */

import { execSync, spawn } from 'child_process'
import { promises as fs } from 'fs'
import path from 'path'

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
}

class DockerManager {
  constructor() {
    this.platform = process.platform
    this.logFile = path.join(process.cwd(), 'logs', 'docker-manager.log')
    this.ensureLogDirectory()
  }

  async ensureLogDirectory() {
    try {
      await fs.mkdir(path.join(process.cwd(), 'logs'), { recursive: true })
    } catch (error) {
      // Directory might already exist
    }
  }

  log(message, level = 'INFO') {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${level}: ${message}\n`
    
    console.log(`${COLORS.CYAN}[${level}]${COLORS.RESET} ${message}`)
    
    // Append to log file
    fs.appendFile(this.logFile, logEntry).catch(() => {})
  }

  error(message) {
    this.log(`${COLORS.RED}${message}${COLORS.RESET}`, 'ERROR')
  }

  success(message) {
    this.log(`${COLORS.GREEN}${message}${COLORS.RESET}`, 'SUCCESS')
  }

  warning(message) {
    this.log(`${COLORS.YELLOW}${message}${COLORS.RESET}`, 'WARNING')
  }

  info(message) {
    this.log(`${COLORS.BLUE}${message}${COLORS.RESET}`, 'INFO')
  }

  /**
   * Check if Docker Desktop is running on macOS
   */
  async isDockerDesktopRunning() {
    try {
      if (this.platform !== 'darwin') return null
      
      const result = execSync('pgrep -f "Docker Desktop"', { encoding: 'utf8', stdio: 'pipe' })
      return result.trim().length > 0
    } catch (error) {
      return false
    }
  }

  /**
   * Check Docker daemon connectivity
   */
  async isDockerDaemonAccessible() {
    try {
      execSync('docker info', { stdio: 'pipe' })
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Get Docker version info
   */
  async getDockerInfo() {
    try {
      const version = execSync('docker --version', { encoding: 'utf8', stdio: 'pipe' }).trim()
      const info = execSync('docker info --format "{{.ServerVersion}}"', { encoding: 'utf8', stdio: 'pipe' }).trim()
      return { version, serverVersion: info }
    } catch (error) {
      return null
    }
  }

  /**
   * Fix Docker socket path issue on macOS
   */
  async fixDockerSocketPath() {
    if (this.platform !== 'darwin') return false

    this.info('Fixing Docker socket path on macOS...')
    
    try {
      // Common Docker socket paths
      const possiblePaths = [
        '/var/run/docker.sock',
        '/Users/' + process.env.USER + '/.docker/run/docker.sock',
        '/Users/' + process.env.USER + '/.docker/desktop/docker.sock'
      ]

      let workingPath = null
      for (const socketPath of possiblePaths) {
        try {
          await fs.access(socketPath)
          // Test if this socket works
          execSync(`DOCKER_HOST=unix://${socketPath} docker info`, { stdio: 'pipe' })
          workingPath = socketPath
          break
        } catch (error) {
          continue
        }
      }

      if (workingPath) {
        this.success(`Found working Docker socket at: ${workingPath}`)
        
        // Set environment variable for current session
        process.env.DOCKER_HOST = `unix://${workingPath}`
        
        // Create shell script to set environment
        const shellScript = `#!/bin/bash
# Docker socket path fix for macOS
export DOCKER_HOST=unix://${workingPath}
echo "Docker socket path set to: $DOCKER_HOST"
`
        await fs.writeFile(path.join(process.cwd(), 'scripts', 'docker-env.sh'), shellScript)
        execSync(`chmod +x ${path.join(process.cwd(), 'scripts', 'docker-env.sh')}`)
        
        this.info('Created docker-env.sh script for permanent fix')
        this.info('Run: source scripts/docker-env.sh')
        
        return true
      } else {
        this.error('Could not find working Docker socket')
        return false
      }
    } catch (error) {
      this.error(`Failed to fix Docker socket: ${error.message}`)
      return false
    }
  }

  /**
   * Start Docker Desktop on macOS
   */
  async startDockerDesktop() {
    if (this.platform !== 'darwin') {
      this.warning('Docker Desktop auto-start only supported on macOS')
      return false
    }

    this.info('Starting Docker Desktop...')
    
    try {
      // Check if Docker Desktop app exists
      await fs.access('/Applications/Docker.app')
      
      // Start Docker Desktop
      execSync('open -a Docker', { stdio: 'pipe' })
      
      this.info('Docker Desktop starting... (this may take 30-60 seconds)')
      
      // Wait for Docker to be ready
      let attempts = 0
      const maxAttempts = 30
      
      while (attempts < maxAttempts) {
        try {
          execSync('docker info', { stdio: 'pipe' })
          this.success('Docker Desktop started successfully!')
          return true
        } catch (error) {
          process.stdout.write('.')
          await new Promise(resolve => setTimeout(resolve, 2000))
          attempts++
        }
      }
      
      console.log('') // New line after dots
      this.error('Docker Desktop failed to start within 60 seconds')
      return false
      
    } catch (error) {
      this.error(`Failed to start Docker Desktop: ${error.message}`)
      return false
    }
  }

  /**
   * Restart Docker Desktop
   */
  async restartDockerDesktop() {
    if (this.platform !== 'darwin') {
      this.warning('Docker Desktop restart only supported on macOS')
      return false
    }

    this.info('Restarting Docker Desktop...')
    
    try {
      // Stop Docker Desktop
      this.info('Stopping Docker Desktop...')
      try {
        execSync('osascript -e "quit app \\"Docker\\""', { stdio: 'pipe' })
      } catch (error) {
        // Fallback to pkill
        execSync('pkill -f "Docker Desktop"', { stdio: 'pipe' })
      }
      
      // Wait for shutdown
      await new Promise(resolve => setTimeout(resolve, 5000))
      
      // Start Docker Desktop
      return await this.startDockerDesktop()
      
    } catch (error) {
      this.error(`Failed to restart Docker Desktop: ${error.message}`)
      return false
    }
  }

  /**
   * Check for Docker processes that might be causing issues
   */
  async checkDockerProcesses() {
    this.info('Checking Docker processes...')
    
    try {
      // Check for Docker processes
      const processes = execSync('ps aux | grep -i docker | grep -v grep', { encoding: 'utf8', stdio: 'pipe' })
      
      if (processes.trim()) {
        this.info('Found Docker processes:')
        console.log(processes)
        return true
      } else {
        this.warning('No Docker processes found')
        return false
      }
    } catch (error) {
      this.warning('Could not check Docker processes')
      return false
    }
  }

  /**
   * Fix Docker permissions
   */
  async fixDockerPermissions() {
    this.info('Fixing Docker permissions...')
    
    try {
      if (this.platform === 'darwin') {
        // macOS Docker Desktop usually doesn't require manual permission fixes
        // But we can check if the socket is accessible
        const socketPaths = [
          '/var/run/docker.sock',
          '/Users/' + process.env.USER + '/.docker/run/docker.sock'
        ]
        
        for (const socketPath of socketPaths) {
          try {
            await fs.access(socketPath)
            const stats = await fs.stat(socketPath)
            this.info(`Socket ${socketPath} exists with permissions: ${stats.mode.toString(8)}`)
          } catch (error) {
            this.info(`Socket ${socketPath} not accessible`)
          }
        }
      } else {
        // Linux - add user to docker group
        try {
          execSync(`sudo usermod -aG docker $USER`, { stdio: 'pipe' })
          this.success('Added user to docker group')
          this.warning('Please log out and log back in for group changes to take effect')
        } catch (error) {
          this.warning('Could not add user to docker group')
        }
      }
      
      return true
    } catch (error) {
      this.error(`Failed to fix permissions: ${error.message}`)
      return false
    }
  }

  /**
   * Comprehensive Docker diagnosis
   */
  async diagnoseDocker() {
    this.info('Running comprehensive Docker diagnosis...')
    
    const diagnosis = {
      dockerInstalled: false,
      dockerDesktopRunning: null,
      daemonAccessible: false,
      version: null,
      issues: [],
      recommendations: []
    }

    // Check 1: Docker installation
    try {
      execSync('docker --version', { stdio: 'pipe' })
      diagnosis.dockerInstalled = true
      this.success('Docker CLI is installed')
    } catch (error) {
      diagnosis.dockerInstalled = false
      diagnosis.issues.push('Docker CLI not installed')
      diagnosis.recommendations.push('Install Docker Desktop from https://docker.com')
    }

    // Check 2: Docker Desktop (macOS)
    if (this.platform === 'darwin') {
      diagnosis.dockerDesktopRunning = await this.isDockerDesktopRunning()
      if (diagnosis.dockerDesktopRunning) {
        this.success('Docker Desktop is running')
      } else {
        this.warning('Docker Desktop is not running')
        diagnosis.issues.push('Docker Desktop not running')
        diagnosis.recommendations.push('Start Docker Desktop application')
      }
    }

    // Check 3: Daemon accessibility
    diagnosis.daemonAccessible = await this.isDockerDaemonAccessible()
    if (diagnosis.daemonAccessible) {
      this.success('Docker daemon is accessible')
      diagnosis.version = await this.getDockerInfo()
    } else {
      this.error('Docker daemon is not accessible')
      diagnosis.issues.push('Cannot connect to Docker daemon')
      diagnosis.recommendations.push('Fix Docker socket path or restart Docker')
    }

    // Check 4: Socket path (macOS specific)
    if (this.platform === 'darwin' && !diagnosis.daemonAccessible) {
      diagnosis.issues.push('Docker socket path may be incorrect')
      diagnosis.recommendations.push('Run socket path fix')
    }

    return diagnosis
  }

  /**
   * Automatic Docker repair
   */
  async autoRepair() {
    this.info('Starting automatic Docker repair...')
    
    const diagnosis = await this.diagnoseDocker()
    let repairCount = 0

    // Repair 1: Start Docker Desktop if not running
    if (this.platform === 'darwin' && !diagnosis.dockerDesktopRunning) {
      this.info('Repair 1: Starting Docker Desktop...')
      if (await this.startDockerDesktop()) {
        repairCount++
      }
    }

    // Repair 2: Fix socket path if daemon not accessible
    if (!diagnosis.daemonAccessible && this.platform === 'darwin') {
      this.info('Repair 2: Fixing Docker socket path...')
      if (await this.fixDockerSocketPath()) {
        repairCount++
      }
    }

    // Repair 3: Fix permissions
    this.info('Repair 3: Fixing Docker permissions...')
    if (await this.fixDockerPermissions()) {
      repairCount++
    }

    // Repair 4: Restart Docker if still not accessible
    if (!await this.isDockerDaemonAccessible() && this.platform === 'darwin') {
      this.info('Repair 4: Restarting Docker Desktop...')
      if (await this.restartDockerDesktop()) {
        repairCount++
      }
    }

    this.success(`Completed ${repairCount} repair actions`)
    
    // Re-diagnose to check if issues are fixed
    const finalDiagnosis = await this.diagnoseDocker()
    
    if (finalDiagnosis.daemonAccessible) {
      this.success('Docker is now working correctly! 🎉')
      return true
    } else {
      this.error('Some issues remain. Manual intervention may be required.')
      this.displayRecommendations(finalDiagnosis.recommendations)
      return false
    }
  }

  /**
   * Display recommendations
   */
  displayRecommendations(recommendations) {
    if (recommendations.length > 0) {
      this.info('Recommendations:')
      recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`)
      })
    }
  }

  /**
   * Show Docker status
   */
  async showStatus() {
    this.info('Docker Status Report')
    console.log('─'.repeat(50))
    
    const diagnosis = await this.diagnoseDocker()
    
    // Installation status
    console.log(`Docker CLI: ${diagnosis.dockerInstalled ? '✅ Installed' : '❌ Not installed'}`)
    
    if (diagnosis.version) {
      console.log(`Version: ${diagnosis.version.version}`)
      console.log(`Server: ${diagnosis.version.serverVersion}`)
    }
    
    // Platform specific info
    if (this.platform === 'darwin') {
      const desktopStatus = diagnosis.dockerDesktopRunning ? '✅ Running' : '❌ Not running'
      console.log(`Docker Desktop: ${desktopStatus}`)
    }
    
    // Daemon status
    const daemonStatus = diagnosis.daemonAccessible ? '✅ Accessible' : '❌ Not accessible'
    console.log(`Docker Daemon: ${daemonStatus}`)
    
    // Show running containers if accessible
    if (diagnosis.daemonAccessible) {
      try {
        const containers = execSync('docker ps --format "table {{.Names}}\t{{.Status}}"', { encoding: 'utf8' })
        console.log('\nRunning Containers:')
        console.log(containers)
      } catch (error) {
        this.warning('Could not list containers')
      }
    }
    
    // Show issues and recommendations
    if (diagnosis.issues.length > 0) {
      console.log('\n⚠️  Issues found:')
      diagnosis.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`)
      })
      
      console.log('\n💡 Recommendations:')
      diagnosis.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`)
      })
    }
  }

  /**
   * Create environment fix script
   */
  async createEnvironmentFix() {
    if (this.platform !== 'darwin') return

    const envScript = `#!/bin/bash
# Docker Environment Fix for macOS
# This script sets the correct Docker socket path

echo "🔧 Setting up Docker environment..."

# Try different socket paths
SOCKET_PATHS=(
    "/var/run/docker.sock"
    "/Users/<USER>/.docker/run/docker.sock"
    "/Users/<USER>/.docker/desktop/docker.sock"
)

for SOCKET_PATH in "\${SOCKET_PATHS[@]}"; do
    if [ -S "$SOCKET_PATH" ]; then
        export DOCKER_HOST="unix://$SOCKET_PATH"
        echo "✅ Docker socket found at: $SOCKET_PATH"
        echo "🚀 Docker environment configured!"
        
        # Test the connection
        if docker info >/dev/null 2>&1; then
            echo "✅ Docker daemon is accessible"
            break
        else
            echo "⚠️  Socket exists but daemon not accessible"
        fi
    fi
done

if [ -z "$DOCKER_HOST" ]; then
    echo "❌ No working Docker socket found"
    echo "💡 Try running: npm run docker:repair"
fi
`

    await fs.writeFile(path.join(process.cwd(), 'scripts', 'docker-env.sh'), envScript)
    execSync(`chmod +x ${path.join(process.cwd(), 'scripts', 'docker-env.sh')}`)
    
    this.success('Created docker-env.sh script')
    this.info('To use: source scripts/docker-env.sh')
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2)
  const command = args[0]

  const dockerManager = new DockerManager()

  try {
    switch (command) {
      case 'status':
        await dockerManager.showStatus()
        break

      case 'diagnose':
        const diagnosis = await dockerManager.diagnoseDocker()
        if (diagnosis.issues.length === 0) {
          dockerManager.success('No Docker issues found! 🎉')
        } else {
          dockerManager.displayRecommendations(diagnosis.recommendations)
        }
        break

      case 'repair':
        await dockerManager.autoRepair()
        break

      case 'start':
        if (await dockerManager.startDockerDesktop()) {
          process.exit(0)
        } else {
          process.exit(1)
        }
        break

      case 'restart':
        if (await dockerManager.restartDockerDesktop()) {
          process.exit(0)
        } else {
          process.exit(1)
        }
        break

      case 'fix-socket':
        await dockerManager.fixDockerSocketPath()
        break

      case 'create-env':
        await dockerManager.createEnvironmentFix()
        break

      case 'processes':
        await dockerManager.checkDockerProcesses()
        break

      default:
        console.log(`
${COLORS.BOLD}Docker Manager for PSII${COLORS.RESET}

Usage: node docker-manager.js <command>

Commands:
  status      Show comprehensive Docker status
  diagnose    Diagnose Docker issues
  repair      Automatically repair Docker issues
  start       Start Docker Desktop
  restart     Restart Docker Desktop
  fix-socket  Fix Docker socket path (macOS)
  create-env  Create environment fix script
  processes   Show Docker processes

Examples:
  node scripts/docker-manager.js status
  node scripts/docker-manager.js repair
  node scripts/docker-manager.js fix-socket
`)
        break
    }
  } catch (error) {
    dockerManager.error(`Command failed: ${error.message}`)
    process.exit(1)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export default DockerManager
