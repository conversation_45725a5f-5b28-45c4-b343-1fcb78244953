#!/usr/bin/env node

// Production-grade database seeding script
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = 'http://127.0.0.1:54331'
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

const adminClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: { autoRefreshToken: false, persistSession: false }
})

async function seedUsers() {
  console.log('🚀 Starting production-grade user seeding...')
  
  try {
    // Check existing auth users
    const { data: existingUsers } = await adminClient.auth.admin.listUsers()
    const existingEmails = existingUsers.users?.map(u => u.email) || []
    console.log('📋 Existing auth users:', existingEmails)

    const testUsers = [
      { email: '<EMAIL>', password: 'password123', role: 'admin' },
      { email: '<EMAIL>', password: 'password123', role: 'manager' },
      { email: '<EMAIL>', password: 'password123', role: 'recruiter' }
    ]

    for (const userInfo of testUsers) {
      if (existingEmails.includes(userInfo.email)) {
        console.log(`✅ ${userInfo.email} already exists`)
        continue
      }

      console.log(`🔨 Creating ${userInfo.email}...`)

      // Create auth user
      const { data: authUser, error: authError } = await adminClient.auth.admin.createUser({
        email: userInfo.email,
        password: userInfo.password,
        email_confirm: true,
        user_metadata: { role: userInfo.role }
      })

      if (authError) {
        console.error(`❌ Failed to create ${userInfo.email}:`, authError.message)
        continue
      }

      // Update corresponding user record
      const { error: updateError } = await adminClient
        .from('users')
        .update({ id: authUser.user.id })
        .eq('email', userInfo.email)

      if (updateError) {
        console.error(`⚠️ Failed to link user record for ${userInfo.email}:`, updateError.message)
      } else {
        console.log(`✅ Successfully created and linked ${userInfo.email}`)
      }
    }

    console.log('🎉 User seeding complete!')

  } catch (error) {
    console.error('💥 Seeding failed:', error)
    process.exit(1)
  }
}

if (import.meta.url === new URL(process.argv[1], 'file://').href) {
  seedUsers()
}

export { seedUsers }
