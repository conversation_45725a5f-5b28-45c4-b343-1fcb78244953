#!/usr/bin/env node

/**
 * Quick Supabase Cloud Connection Test
 * Tests the connection to your Supabase Cloud instance
 */

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'

// Your Supabase Cloud credentials
const SUPABASE_URL = 'https://hfzhvrknjgwtrkgyinjf.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjgyODIsImV4cCI6MjA2NTY0NDI4Mn0.TwvhR8RrtEZuV6oCXIT2fho5_U5mnH21Hvt4bbvhThY'
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhmemh2cmtuamd3dHJrZ3lpbmpmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODI4MiwiZXhwIjoyMDY1NjQ0MjgyfQ._scXNK1IgQuVok-iIBVxQYmcWntrI2VLInx1_Cz8knI'

function log(message, type = 'info') {
  const timestamp = new Date().toISOString()
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
  console.log(`${prefix} [${timestamp}] ${message}`)
}

async function testConnection() {
  log('🌐 Testing Supabase Cloud connection...')
  log(`📍 Project URL: ${SUPABASE_URL}`)
  log('')
  
  // Test anon client
  log('Testing anonymous client connection...')
  const anonClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
  
  try {
    // Test basic connectivity
    const { data, error } = await anonClient.from('companies').select('count').limit(1)
    if (error) {
      if (error.message.includes('permission denied') || error.message.includes('relation') || error.message.includes('does not exist')) {
        log('✅ Anon client connected (no data/tables yet - this is normal)', 'success')
      } else {
        log(`⚠️ Anon client connection issue: ${error.message}`, 'warning')
      }
    } else {
      log('✅ Anon client connection successful', 'success')
    }
  } catch (err) {
    log(`❌ Anon client error: ${err.message}`, 'error')
  }
  
  // Test service client
  log('Testing service role client connection...')
  const serviceClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)
  
  try {
    const { data, error } = await serviceClient.from('companies').select('count').limit(1)
    if (error) {
      if (error.message.includes('relation') || error.message.includes('does not exist')) {
        log('✅ Service client connected (tables need to be created)', 'success')
      } else {
        log(`⚠️ Service client issue: ${error.message}`, 'warning')
      }
    } else {
      log('✅ Service client connection successful', 'success')
    }
  } catch (err) {
    log(`❌ Service client error: ${err.message}`, 'error')
  }
  
  // Test auth service
  log('Testing authentication service...')
  try {
    const { data: { user }, error } = await anonClient.auth.getUser()
    log('✅ Auth service accessible (no user logged in - this is normal)', 'success')
  } catch (err) {
    log(`❌ Auth service error: ${err.message}`, 'error')
  }
  
  log('')
  log('🎉 Cloud connection test completed!')
  log('')
  log('Next steps:')
  log('1. Set up your database schema in Supabase Dashboard')
  log('2. Create test users')
  log('3. Start development: npm run dev:cloud')
  log('')
  log('📖 Supabase Dashboard: https://app.supabase.com/project/hfzhvrknjgwtrkgyinjf')
}

async function checkEnvironmentFiles() {
  log('🔍 Checking environment configuration...')

  // Check customer app env
  const customerEnvPath = 'apps/customer-app/.env.local'
  if (fs.existsSync(customerEnvPath)) {
    const content = fs.readFileSync(customerEnvPath, 'utf8')
    if (content.includes('hfzhvrknjgwtrkgyinjf.supabase.co')) {
      log('✅ Customer app configured for Supabase Cloud', 'success')
    } else {
      log('❌ Customer app not configured for cloud', 'error')
    }
  } else {
    log('❌ Customer app .env.local missing', 'error')
  }
  
  // Check console app env
  const consoleEnvPath = 'apps/console-app/.env.local'
  if (fs.existsSync(consoleEnvPath)) {
    const content = fs.readFileSync(consoleEnvPath, 'utf8')
    if (content.includes('hfzhvrknjgwtrkgyinjf.supabase.co')) {
      log('✅ Console app configured for Supabase Cloud', 'success')
    } else {
      log('❌ Console app not configured for cloud', 'error')
    }
  } else {
    log('❌ Console app .env.local missing', 'error')
  }
}

async function main() {
  try {
    await checkEnvironmentFiles()
    log('')
    await testConnection()
  } catch (error) {
    log(`Fatal error: ${error.message}`, 'error')
    process.exit(1)
  }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { testConnection, checkEnvironmentFiles }
