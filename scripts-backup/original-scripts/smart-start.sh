#!/bin/bash

# PSII Smart Startup Script
# Automatically manages ports and starts all services

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
PORT_MANAGER="$SCRIPT_DIR/port-manager.js"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}${WHITE}$1${NC}"
}

# Create logs directory
mkdir -p "$PROJECT_ROOT/logs"

# Function to check if Node.js is available
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed or not in PATH"
        exit 1
    fi
}

# Function to check if npm is available
check_npm() {
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed or not in PATH"
        exit 1
    fi
}

# Function to check if Supabase CLI is available
check_supabase() {
    if ! command -v supabase &> /dev/null; then
        log_warning "Supabase CLI not found. Installing..."
        npm install -g supabase
    fi
}

# Function to check Docker health
check_docker() {
    log_info "Checking Docker health..."
    
    # Check if Docker is accessible
    if docker info > /dev/null 2>&1; then
        log_success "Docker is accessible"
        return 0
    else
        log_warning "Docker daemon not accessible"
        
        # Try to fix Docker issues automatically
        log_info "Attempting to fix Docker issues..."
        node "$SCRIPT_DIR/docker-manager.js" repair
        
        # Check again after repair
        if docker info > /dev/null 2>&1; then
            log_success "Docker issues resolved!"
            return 0
        else
            log_error "Could not resolve Docker issues. Please check Docker Desktop."
            log_info "Try: node scripts/docker-manager.js status"
            exit 1
        fi
    fi
}

# Function to check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    check_node
    check_npm
    check_supabase
    log_success "All dependencies are available"
}

# Function to run port management
manage_ports() {
    log_info "Managing port conflicts..."
    cd "$PROJECT_ROOT"
    
    if node "$PORT_MANAGER" check > /dev/null 2>&1; then
        log_success "No port conflicts detected"
    else
        log_warning "Port conflicts detected. Resolving..."
        node "$PORT_MANAGER" resolve --force
    fi
}

# Function to start Supabase
start_supabase() {
    log_info "Starting Supabase services..."
    cd "$PROJECT_ROOT"
    
    # Check if Supabase is already running
    if node "$PORT_MANAGER" status | grep -q "RUNNING.*54331" > /dev/null 2>&1; then
        log_success "Supabase is already running"
    else
        npx supabase start
        log_success "Supabase services started"
    fi
}

# Function to start customer app
start_customer_app() {
    log_info "Starting Customer App on port 3004..."
    cd "$PROJECT_ROOT/apps/customer-app"
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        log_info "Installing Customer App dependencies..."
        npm install
    fi
    
    # Start in background
    npm run dev > "$PROJECT_ROOT/logs/customer-app.log" 2>&1 &
    CUSTOMER_PID=$!
    echo $CUSTOMER_PID > "$PROJECT_ROOT/logs/customer-app.pid"
    
    log_success "Customer App started (PID: $CUSTOMER_PID)"
}

# Function to start console app
start_console_app() {
    log_info "Starting Console App on port 3005..."
    cd "$PROJECT_ROOT/apps/console-app"
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        log_info "Installing Console App dependencies..."
        npm install
    fi
    
    # Start in background
    npm run dev > "$PROJECT_ROOT/logs/console-app.log" 2>&1 &
    CONSOLE_PID=$!
    echo $CONSOLE_PID > "$PROJECT_ROOT/logs/console-app.pid"
    
    log_success "Console App started (PID: $CONSOLE_PID)"
}

# Function to wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Wait for Supabase API
    for i in {1..30}; do
        if curl -s http://127.0.0.1:54331/rest/v1/ > /dev/null 2>&1; then
            log_success "Supabase API is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Supabase API failed to start"
            exit 1
        fi
        sleep 2
    done
    
    # Wait for Customer App
    for i in {1..30}; do
        if curl -s http://localhost:3004 > /dev/null 2>&1; then
            log_success "Customer App is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Customer App failed to start"
            exit 1
        fi
        sleep 2
    done
    
    # Wait for Console App
    for i in {1..30}; do
        if curl -s http://localhost:3005 > /dev/null 2>&1; then
            log_success "Console App is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Console App failed to start"
            exit 1
        fi
        sleep 2
    done
}

# Function to display service URLs
show_urls() {
    log_header "🚀 PSII Development Environment Ready!"
    echo ""
    echo -e "${CYAN}Application URLs:${NC}"
    echo -e "  Customer App:     ${WHITE}http://localhost:3004${NC}"
    echo -e "  Console App:      ${WHITE}http://localhost:3005${NC}"
    echo ""
    echo -e "${CYAN}Supabase URLs:${NC}"
    echo -e "  API:              ${WHITE}http://127.0.0.1:54331${NC}"
    echo -e "  Studio:           ${WHITE}http://127.0.0.1:54333${NC}"
    echo -e "  Database:         ${WHITE}postgresql://postgres:postgres@127.0.0.1:54332/postgres${NC}"
    echo ""
    echo -e "${CYAN}Development Tools:${NC}"
    echo -e "  Logs Directory:   ${WHITE}$PROJECT_ROOT/logs/${NC}"
    echo -e "  Port Manager:     ${WHITE}node scripts/port-manager.js status${NC}"
    echo ""
    echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"
}

# Function to cleanup on exit
cleanup() {
    log_info "Shutting down services..."
    
    # Kill background processes
    if [ -f "$PROJECT_ROOT/logs/customer-app.pid" ]; then
        CUSTOMER_PID=$(cat "$PROJECT_ROOT/logs/customer-app.pid")
        kill $CUSTOMER_PID > /dev/null 2>&1 || true
        rm -f "$PROJECT_ROOT/logs/customer-app.pid"
    fi
    
    if [ -f "$PROJECT_ROOT/logs/console-app.pid" ]; then
        CONSOLE_PID=$(cat "$PROJECT_ROOT/logs/console-app.pid")
        kill $CONSOLE_PID > /dev/null 2>&1 || true
        rm -f "$PROJECT_ROOT/logs/console-app.pid"
    fi
    
    log_success "Services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    log_header "=== PSII Smart Startup ==="
    
    # Check if running from project root
    if [ ! -f "$PROJECT_ROOT/package.json" ]; then
        log_error "Please run this script from the PSII project root"
        exit 1
    fi
    
    # Step 1: Check dependencies
    check_dependencies
    
    # Step 2: Check Docker health
    check_docker
    
    # Step 3: Manage ports
    manage_ports
    
    # Step 3: Start Supabase
    start_supabase
    
    # Step 4: Generate environment config
    cd "$PROJECT_ROOT"
    node "$PORT_MANAGER" env
    
    # Step 5: Start applications
    start_customer_app
    start_console_app
    
    # Step 6: Wait for services
    wait_for_services
    
    # Step 7: Show URLs and wait
    show_urls
    
    # Step 8: Keep running until interrupted
    while true; do
        sleep 1
    done
}

# Check command line arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "PSII Smart Startup Script"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help, -h     Show this help message"
    echo "  --status       Show service status only"
    echo "  --stop         Stop all services"
    echo ""
    echo "This script will:"
    echo "  1. Check and resolve port conflicts"
    echo "  2. Start Supabase services"
    echo "  3. Start Customer App (port 3004)"
    echo "  4. Start Console App (port 3005)"
    echo "  5. Wait for all services to be ready"
    echo "  6. Display service URLs"
    exit 0
elif [ "$1" = "--status" ]; then
    cd "$PROJECT_ROOT"
    node "$PORT_MANAGER" status
    exit 0
elif [ "$1" = "--stop" ]; then
    cd "$PROJECT_ROOT"
    node "$PORT_MANAGER" stop
    # Also kill our app processes
    if [ -f "$PROJECT_ROOT/logs/customer-app.pid" ]; then
        CUSTOMER_PID=$(cat "$PROJECT_ROOT/logs/customer-app.pid")
        kill $CUSTOMER_PID > /dev/null 2>&1 || true
        rm -f "$PROJECT_ROOT/logs/customer-app.pid"
    fi
    if [ -f "$PROJECT_ROOT/logs/console-app.pid" ]; then
        CONSOLE_PID=$(cat "$PROJECT_ROOT/logs/console-app.pid")
        kill $CONSOLE_PID > /dev/null 2>&1 || true
        rm -f "$PROJECT_ROOT/logs/console-app.pid"
    fi
    log_success "All services stopped"
    exit 0
else
    main
fi
