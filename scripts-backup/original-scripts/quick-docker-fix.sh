#!/bin/bash

# Quick Docker Fix for macOS Socket Issue
# Resolves the "Cannot connect to the Docker daemon" error

echo "🔧 Quick Docker Fix for macOS"
echo "=============================="

# Check if Docker Desktop is running
if ! pgrep -f "Docker Desktop" > /dev/null; then
    echo "⚠️  Docker Desktop is not running"
    echo "🚀 Starting Docker Desktop..."
    open -a Docker
    echo "⏳ Waiting for Docker Desktop to start..."
    sleep 10
fi

# Try different socket paths to fix the connection
echo "🔍 Finding the correct Docker socket path..."

SOCKET_PATHS=(
    "/var/run/docker.sock"
    "/Users/<USER>/.docker/run/docker.sock" 
    "/Users/<USER>/.docker/desktop/docker.sock"
)

WORKING_SOCKET=""

for SOCKET_PATH in "${SOCKET_PATHS[@]}"; do
    if [ -S "$SOCKET_PATH" ]; then
        echo "📍 Testing socket: $SOCKET_PATH"
        if DOCKER_HOST="unix://$SOCKET_PATH" docker info >/dev/null 2>&1; then
            WORKING_SOCKET="$SOCKET_PATH"
            echo "✅ Found working socket: $SOCKET_PATH"
            break
        fi
    fi
done

if [ -n "$WORKING_SOCKET" ]; then
    # Export for current session
    export DOCKER_HOST="unix://$WORKING_SOCKET"
    
    # Create permanent fix
    echo "💾 Creating permanent fix..."
    
    # Add to shell profile
    SHELL_PROFILE=""
    if [ -f "$HOME/.zshrc" ]; then
        SHELL_PROFILE="$HOME/.zshrc"
    elif [ -f "$HOME/.bash_profile" ]; then
        SHELL_PROFILE="$HOME/.bash_profile"
    elif [ -f "$HOME/.bashrc" ]; then
        SHELL_PROFILE="$HOME/.bashrc"
    fi
    
    if [ -n "$SHELL_PROFILE" ]; then
        # Check if already exists
        if ! grep -q "DOCKER_HOST.*$WORKING_SOCKET" "$SHELL_PROFILE"; then
            echo "" >> "$SHELL_PROFILE"
            echo "# Docker socket fix for macOS" >> "$SHELL_PROFILE"
            echo "export DOCKER_HOST=unix://$WORKING_SOCKET" >> "$SHELL_PROFILE"
            echo "✅ Added Docker socket fix to $SHELL_PROFILE"
        else
            echo "ℹ️  Docker socket fix already exists in $SHELL_PROFILE"
        fi
    fi
    
    # Test the fix
    echo "🧪 Testing Docker connection..."
    if docker info >/dev/null 2>&1; then
        echo "🎉 SUCCESS! Docker is now working!"
        echo ""
        echo "📊 Docker Status:"
        docker info --format "Docker version: {{.ServerVersion}}"
        echo "Running containers: $(docker ps --format 'table {{.Names}}\t{{.Status}}' | wc -l | xargs echo)"
        echo ""
        echo "🔧 To apply the fix permanently, restart your terminal or run:"
        echo "   source $SHELL_PROFILE"
    else
        echo "❌ Still having issues. Try running Docker Desktop manually."
    fi
    
else
    echo "❌ Could not find a working Docker socket"
    echo "💡 Try these steps:"
    echo "   1. Make sure Docker Desktop is running"
    echo "   2. Restart Docker Desktop"
    echo "   3. Check Docker Desktop settings"
    echo "   4. Run: npm run docker:repair"
fi

echo ""
echo "🔍 For more detailed diagnosis, run: npm run docker:status"
