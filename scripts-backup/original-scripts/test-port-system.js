#!/usr/bin/env node

/**
 * PSII Port Management System Test
 * Verifies that the port management system works correctly
 */

import PortManager from './port-manager.js'

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
}

class PortSystemTest {
  constructor() {
    this.portManager = new PortManager()
    this.testResults = []
  }

  log(message, color = COLORS.CYAN) {
    console.log(`${color}[TEST]${COLORS.RESET} ${message}`)
  }

  success(message) {
    console.log(`${COLORS.GREEN}[PASS]${COLORS.RESET} ${message}`)
    this.testResults.push({ test: message, status: 'PASS' })
  }

  fail(message) {
    console.log(`${COLORS.RED}[FAIL]${COLORS.RESET} ${message}`)
    this.testResults.push({ test: message, status: 'FAIL' })
  }

  async testPortDetection() {
    this.log('Testing port detection...')
    
    try {
      // Test with a port that should be free
      const freePort = await this.portManager.isPortInUse(59999)
      if (!freePort) {
        this.success('Free port detection works correctly')
      } else {
        this.fail('Free port incorrectly detected as in use')
      }

      // Test port conflict detection
      const conflicts = await this.portManager.checkPortConflicts()
      this.success(`Port conflict detection completed (found ${conflicts.length} conflicts)`)
      
    } catch (error) {
      this.fail(`Port detection failed: ${error.message}`)
    }
  }

  async testPortManagerCommands() {
    this.log('Testing port manager commands...')
    
    try {
      // Test status command
      const serviceStatus = await this.portManager.verifyServices()
      if (serviceStatus) {
        this.success('Service status check works')
      } else {
        this.fail('Service status check failed')
      }

      // Test environment generation
      await this.portManager.generateEnvConfig()
      this.success('Environment configuration generation works')
      
    } catch (error) {
      this.fail(`Port manager commands failed: ${error.message}`)
    }
  }

  async testConfigurationFiles() {
    this.log('Testing configuration files...')
    
    try {
      const fs = await import('fs/promises')
      
      // Check if environment templates exist
      const envExample = await fs.access('/Users/<USER>/Desktop/PSII/.env.example').then(() => true).catch(() => false)
      if (envExample) {
        this.success('Root .env.example exists')
      } else {
        this.fail('Root .env.example missing')
      }

      // Check customer app env example
      const customerEnv = await fs.access('/Users/<USER>/Desktop/PSII/apps/customer-app/.env.example').then(() => true).catch(() => false)
      if (customerEnv) {
        this.success('Customer app .env.example exists')
      } else {
        this.fail('Customer app .env.example missing')
      }

      // Check console app env example
      const consoleEnv = await fs.access('/Users/<USER>/Desktop/PSII/apps/console-app/.env.example').then(() => true).catch(() => false)
      if (consoleEnv) {
        this.success('Console app .env.example exists')
      } else {
        this.fail('Console app .env.example missing')
      }

    } catch (error) {
      this.fail(`Configuration file check failed: ${error.message}`)
    }
  }

  async testScriptFiles() {
    this.log('Testing script files...')
    
    try {
      const fs = await import('fs/promises')
      
      // Check if port manager exists and is executable
      const portManager = await fs.access('/Users/<USER>/Desktop/PSII/scripts/port-manager.js').then(() => true).catch(() => false)
      if (portManager) {
        this.success('Port manager script exists')
      } else {
        this.fail('Port manager script missing')
      }

      // Check if smart start exists and is executable
      const smartStart = await fs.access('/Users/<USER>/Desktop/PSII/scripts/smart-start.sh').then(() => true).catch(() => false)
      if (smartStart) {
        this.success('Smart start script exists')
      } else {
        this.fail('Smart start script missing')
      }

      // Check if quick dev exists
      const quickDev = await fs.access('/Users/<USER>/Desktop/PSII/scripts/quick-dev.js').then(() => true).catch(() => false)
      if (quickDev) {
        this.success('Quick dev script exists')
      } else {
        this.fail('Quick dev script missing')
      }

    } catch (error) {
      this.fail(`Script file check failed: ${error.message}`)
    }
  }

  async testPackageJsonScripts() {
    this.log('Testing package.json scripts...')
    
    try {
      const fs = await import('fs/promises')
      const packageJson = JSON.parse(await fs.readFile('/Users/<USER>/Desktop/PSII/package.json', 'utf8'))
      
      const requiredScripts = [
        'start', 'stop', 'status', 'dev',
        'dev:customer', 'dev:console', 'dev:both', 'dev:supabase',
        'port-check', 'port-resolve', 'port-status',
        'supabase:start', 'supabase:stop'
      ]

      let missingScripts = []
      for (const script of requiredScripts) {
        if (!packageJson.scripts[script]) {
          missingScripts.push(script)
        }
      }

      if (missingScripts.length === 0) {
        this.success('All required npm scripts are defined')
      } else {
        this.fail(`Missing npm scripts: ${missingScripts.join(', ')}`)
      }

    } catch (error) {
      this.fail(`Package.json script check failed: ${error.message}`)
    }
  }

  async testViteConfiguration() {
    this.log('Testing Vite configurations...')
    
    try {
      const fs = await import('fs/promises')
      
      // Check customer app vite config
      const customerVite = await fs.readFile('/Users/<USER>/Desktop/PSII/apps/customer-app/vite.config.ts', 'utf8')
      if (customerVite.includes('port: 3004') && customerVite.includes('strictPort: true')) {
        this.success('Customer app Vite config has correct port settings')
      } else {
        this.fail('Customer app Vite config missing port/strictPort settings')
      }

      // Check console app vite config
      const consoleVite = await fs.readFile('/Users/<USER>/Desktop/PSII/apps/console-app/vite.config.ts', 'utf8')
      if (consoleVite.includes('port: 3005') && consoleVite.includes('strictPort: true')) {
        this.success('Console app Vite config has correct port settings')
      } else {
        this.fail('Console app Vite config missing port/strictPort settings')
      }

    } catch (error) {
      this.fail(`Vite configuration check failed: ${error.message}`)
    }
  }

  async testDockerIntegration() {
    this.log('Testing Docker integration...')
    
    try {
      const fs = await import('fs/promises')
      
      // Check if Docker manager exists
      const dockerManager = await fs.access('/Users/<USER>/Desktop/PSII/scripts/docker-manager.js').then(() => true).catch(() => false)
      if (dockerManager) {
        this.success('Docker manager script exists')
      } else {
        this.fail('Docker manager script missing')
      }

      // Check if quick Docker fix exists
      const quickFix = await fs.access('/Users/<USER>/Desktop/PSII/scripts/quick-docker-fix.sh').then(() => true).catch(() => false)
      if (quickFix) {
        this.success('Quick Docker fix script exists')
      } else {
        this.fail('Quick Docker fix script missing')
      }

    } catch (error) {
      this.fail(`Docker integration check failed: ${error.message}`)
    }
  }

  displayResults() {
    console.log(`\n${COLORS.BOLD}${COLORS.WHITE}=== Test Results ===${COLORS.RESET}`)
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length
    const failed = this.testResults.filter(r => r.status === 'FAIL').length
    const total = this.testResults.length

    console.log(`\n${COLORS.BOLD}Summary:${COLORS.RESET}`)
    console.log(`  Total Tests: ${total}`)
    console.log(`  ${COLORS.GREEN}Passed: ${passed}${COLORS.RESET}`)
    console.log(`  ${COLORS.RED}Failed: ${failed}${COLORS.RESET}`)

    if (failed > 0) {
      console.log(`\n${COLORS.RED}Failed Tests:${COLORS.RESET}`)
      this.testResults
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`  - ${r.test}`))
    }

    const successRate = (passed / total * 100).toFixed(1)
    console.log(`\n${COLORS.BOLD}Success Rate: ${successRate}%${COLORS.RESET}`)

    if (failed === 0) {
      console.log(`\n${COLORS.GREEN}${COLORS.BOLD}🎉 All tests passed! Port management system is ready.${COLORS.RESET}`)
      return true
    } else {
      console.log(`\n${COLORS.RED}${COLORS.BOLD}❌ Some tests failed. Please check the configuration.${COLORS.RESET}`)
      return false
    }
  }

  async runAllTests() {
    console.log(`${COLORS.BOLD}${COLORS.MAGENTA}PSII Port Management System Test Suite${COLORS.RESET}\n`)

    await this.testPortDetection()
    await this.testPortManagerCommands()
    await this.testConfigurationFiles()
    await this.testScriptFiles()
    await this.testPackageJsonScripts()
    await this.testViteConfiguration()
    await this.testDockerIntegration()

    return this.displayResults()
  }
}

async function main() {
  const test = new PortSystemTest()
  const success = await test.runAllTests()
  process.exit(success ? 0 : 1)
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export default PortSystemTest
