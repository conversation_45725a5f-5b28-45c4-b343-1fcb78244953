# SQL Scripts

This directory contains SQL scripts for testing and data setup.

## Files

- `create-test-users.sql` - SQL to create test users
- `quick-test-data.sql` - Quick test data insertion
- `simple-test-users.sql` - Simple test user creation

## Usage

These scripts can be run directly in Supabase SQL Editor or through psql:

```bash
psql -h 127.0.0.1 -p 54332 -U postgres -d postgres -f script-name.sql
```

## Note

These are for local development and testing only.
