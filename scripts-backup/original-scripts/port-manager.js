#!/usr/bin/env node

/**
 * PSII Port Manager
 * Manages port conflicts and ensures standard port allocation for development
 */

import { execSync, spawn } from 'child_process'
import { promises as fs } from 'fs'
import path from 'path'

// Standard port configuration for PSII
const PSII_PORTS = {
  // Supabase services
  SUPABASE_API: 54331,
  SUPABASE_DB: 54332,
  SUPABASE_STUDIO: 54333,
  SUPABASE_INBUCKET: 54324,
  SUPABASE_ANALYTICS: 54327,
  SUPABASE_SHADOW_DB: 54330,
  SUPABASE_POOLER: 54329,
  SUPABASE_EDGE_INSPECTOR: 8083,
  
  // Application services
  CUSTOMER_APP: 3004,
  CONSOLE_APP: 3005
}

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
}

class PortManager {
  constructor() {
    this.logFile = path.join(process.cwd(), 'logs', 'port-manager.log')
    this.ensureLogDirectory()
  }

  async ensureLogDirectory() {
    try {
      await fs.mkdir(path.join(process.cwd(), 'logs'), { recursive: true })
    } catch (error) {
      // Directory might already exist
    }
  }

  log(message, level = 'INFO') {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${level}: ${message}\n`
    
    console.log(`${COLORS.CYAN}[${level}]${COLORS.RESET} ${message}`)
    
    // Append to log file
    fs.appendFile(this.logFile, logEntry).catch(() => {})
  }

  error(message) {
    this.log(`${COLORS.RED}${message}${COLORS.RESET}`, 'ERROR')
  }

  success(message) {
    this.log(`${COLORS.GREEN}${message}${COLORS.RESET}`, 'SUCCESS')
  }

  warning(message) {
    this.log(`${COLORS.YELLOW}${message}${COLORS.RESET}`, 'WARNING')
  }

  info(message) {
    this.log(`${COLORS.BLUE}${message}${COLORS.RESET}`, 'INFO')
  }

  /**
   * Check if a port is in use
   */
  async isPortInUse(port) {
    return new Promise((resolve) => {
      try {
        const command = process.platform === 'win32' 
          ? `netstat -ano | findstr :${port}`
          : `lsof -ti:${port}`
        
        execSync(command, { stdio: 'pipe' })
        resolve(true)
      } catch (error) {
        resolve(false)
      }
    })
  }

  /**
   * Get process using a specific port
   */
  getProcessOnPort(port) {
    try {
      const command = process.platform === 'win32'
        ? `netstat -ano | findstr :${port}`
        : `lsof -ti:${port}`
      
      const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' })
      return result.trim()
    } catch (error) {
      return null
    }
  }

  /**
   * Kill process using a specific port
   */
  async killProcessOnPort(port) {
    try {
      if (process.platform === 'win32') {
        // Windows
        const netstatResult = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' })
        const lines = netstatResult.split('\n').filter(line => line.trim())
        
        for (const line of lines) {
          const parts = line.trim().split(/\s+/)
          const pid = parts[parts.length - 1]
          if (pid && !isNaN(pid)) {
            execSync(`taskkill /PID ${pid} /F`, { stdio: 'pipe' })
            this.success(`Killed process ${pid} on port ${port}`)
          }
        }
      } else {
        // Unix-like systems (macOS, Linux)
        const pids = execSync(`lsof -ti:${port}`, { encoding: 'utf8' }).trim().split('\n')
        
        for (const pid of pids) {
          if (pid && !isNaN(pid)) {
            execSync(`kill -9 ${pid}`, { stdio: 'pipe' })
            this.success(`Killed process ${pid} on port ${port}`)
          }
        }
      }
      
      return true
    } catch (error) {
      this.warning(`No process found on port ${port} or failed to kill: ${error.message}`)
      return false
    }
  }

  /**
   * Check all PSII ports for conflicts
   */
  async checkPortConflicts() {
    this.info('Checking for port conflicts...')
    const conflicts = []

    for (const [service, port] of Object.entries(PSII_PORTS)) {
      const inUse = await this.isPortInUse(port)
      if (inUse) {
        const process = this.getProcessOnPort(port)
        conflicts.push({ service, port, process })
      }
    }

    return conflicts
  }

  /**
   * Resolve all port conflicts by killing conflicting processes
   */
  async resolvePortConflicts(conflicts, force = false) {
    if (conflicts.length === 0) {
      this.success('No port conflicts detected!')
      return true
    }

    this.warning(`Found ${conflicts.length} port conflicts:`)
    conflicts.forEach(({ service, port, process }) => {
      console.log(`  - ${service} (${port}): ${process}`)
    })

    if (!force) {
      // In production, you might want to prompt for confirmation
      this.info('Use --force flag to automatically kill conflicting processes')
      return false
    }

    this.info('Resolving port conflicts...')
    let resolved = 0

    for (const { service, port } of conflicts) {
      const killed = await this.killProcessOnPort(port)
      if (killed) {
        resolved++
        // Wait a moment for the port to be freed
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    this.success(`Resolved ${resolved}/${conflicts.length} port conflicts`)
    return resolved === conflicts.length
  }

  /**
   * Verify all PSII services are running on correct ports
   */
  async verifyServices() {
    this.info('Verifying PSII services...')
    const serviceStatus = {}

    for (const [service, port] of Object.entries(PSII_PORTS)) {
      const inUse = await this.isPortInUse(port)
      serviceStatus[service] = {
        port,
        running: inUse,
        status: inUse ? 'RUNNING' : 'STOPPED'
      }
    }

    return serviceStatus
  }

  /**
   * Display service status in a formatted table
   */
  displayServiceStatus(serviceStatus) {
    this.info('Service Status:')
    console.log(`${COLORS.BOLD}${'Service'.padEnd(20)} ${'Port'.padEnd(8)} ${'Status'.padEnd(10)}${COLORS.RESET}`)
    console.log('─'.repeat(50))

    for (const [service, info] of Object.entries(serviceStatus)) {
      const statusColor = info.running ? COLORS.GREEN : COLORS.RED
      const serviceFormatted = service.replace('_', ' ').toLowerCase()
      console.log(
        `${serviceFormatted.padEnd(20)} ${info.port.toString().padEnd(8)} ${statusColor}${info.status}${COLORS.RESET}`
      )
    }
  }

  /**
   * Start Supabase with standard configuration
   */
  async startSupabase() {
    this.info('Starting Supabase services...')
    try {
      const supabaseProcess = spawn('npx', ['supabase', 'start'], {
        stdio: 'inherit',
        cwd: process.cwd()
      })

      return new Promise((resolve, reject) => {
        supabaseProcess.on('close', (code) => {
          if (code === 0) {
            this.success('Supabase services started successfully')
            resolve(true)
          } else {
            this.error(`Supabase failed to start with exit code ${code}`)
            reject(new Error(`Supabase start failed: ${code}`))
          }
        })

        supabaseProcess.on('error', (error) => {
          this.error(`Failed to start Supabase: ${error.message}`)
          reject(error)
        })
      })
    } catch (error) {
      this.error(`Error starting Supabase: ${error.message}`)
      throw error
    }
  }

  /**
   * Stop all PSII services gracefully
   */
  async stopAllServices() {
    this.info('Stopping all PSII services...')
    
    // Stop Supabase first
    try {
      execSync('npx supabase stop', { stdio: 'inherit' })
      this.success('Supabase services stopped')
    } catch (error) {
      this.warning('Failed to stop Supabase services gracefully')
    }

    // Kill any remaining processes on our ports
    for (const [service, port] of Object.entries(PSII_PORTS)) {
      await this.killProcessOnPort(port)
    }

    this.success('All PSII services stopped')
  }

  /**
   * Health check for all services
   */
  async healthCheck() {
    this.info('Performing health check...')
    const serviceStatus = await this.verifyServices()
    this.displayServiceStatus(serviceStatus)

    const runningServices = Object.values(serviceStatus).filter(s => s.running).length
    const totalServices = Object.keys(serviceStatus).length

    if (runningServices === totalServices) {
      this.success('All services are running correctly!')
      return true
    } else {
      this.warning(`${runningServices}/${totalServices} services are running`)
      return false
    }
  }

  /**
   * Generate environment configuration based on current ports
   */
  async generateEnvConfig() {
    const envConfig = `# PSII Standard Port Configuration
# Generated automatically by port-manager.js

# Supabase Configuration
SUPABASE_URL=http://127.0.0.1:${PSII_PORTS.SUPABASE_API}
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:${PSII_PORTS.SUPABASE_DB}/postgres

# Application Ports
CUSTOMER_APP_PORT=${PSII_PORTS.CUSTOMER_APP}
CONSOLE_APP_PORT=${PSII_PORTS.CONSOLE_APP}

# Supabase Studio
SUPABASE_STUDIO_URL=http://127.0.0.1:${PSII_PORTS.SUPABASE_STUDIO}
`

    const envPath = path.join(process.cwd(), '.env.ports')
    await fs.writeFile(envPath, envConfig)
    this.success(`Generated port configuration: ${envPath}`)
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2)
  const command = args[0]
  const flags = args.slice(1)
  const force = flags.includes('--force')

  const portManager = new PortManager()

  try {
    switch (command) {
      case 'check':
        const conflicts = await portManager.checkPortConflicts()
        if (conflicts.length > 0) {
          process.exit(1)
        }
        break

      case 'resolve':
        const allConflicts = await portManager.checkPortConflicts()
        await portManager.resolvePortConflicts(allConflicts, force)
        break

      case 'start':
        // Resolve conflicts first
        const startConflicts = await portManager.checkPortConflicts()
        if (startConflicts.length > 0) {
          await portManager.resolvePortConflicts(startConflicts, true)
        }
        // Start Supabase
        await portManager.startSupabase()
        // Verify everything is running
        await portManager.healthCheck()
        break

      case 'stop':
        await portManager.stopAllServices()
        break

      case 'status':
        await portManager.healthCheck()
        break

      case 'env':
        await portManager.generateEnvConfig()
        break

      case 'health':
        const healthy = await portManager.healthCheck()
        process.exit(healthy ? 0 : 1)
        break

      default:
        console.log(`
${COLORS.BOLD}PSII Port Manager${COLORS.RESET}

Usage: node port-manager.js <command> [options]

Commands:
  check     Check for port conflicts
  resolve   Resolve port conflicts (use --force to auto-kill)
  start     Start all PSII services (auto-resolves conflicts)
  stop      Stop all PSII services
  status    Show service status
  health    Health check (exits 1 if issues found)
  env       Generate .env.ports configuration file

Examples:
  node port-manager.js check
  node port-manager.js resolve --force
  node port-manager.js start
  node port-manager.js status
`)
        break
    }
  } catch (error) {
    portManager.error(`Command failed: ${error.message}`)
    process.exit(1)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export default PortManager
