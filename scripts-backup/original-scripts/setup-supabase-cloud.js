#!/usr/bin/env node

/**
 * Supabase Cloud Setup Script
 * Configures both apps to use Supabase Cloud for development
 * Eliminates local migration issues and simplifies development workflow
 */

import fs from 'fs'
import path from 'path'
import readline from 'readline'

function log(message, type = 'info') {
  const timestamp = new Date().toISOString()
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
  console.log(`${prefix} [${timestamp}] ${message}`)
}

function createBackup(filePath) {
  if (fs.existsSync(filePath)) {
    const backupPath = `${filePath}.backup.${Date.now()}`
    fs.copyFileSync(filePath, backupPath)
    log(`Created backup: ${backupPath}`)
    return backupPath
  }
  return null
}

async function promptForCloudCredentials() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })

  const question = (prompt) => new Promise((resolve) => {
    rl.question(prompt, resolve)
  })

  log('🌐 Setting up Supabase Cloud configuration...')
  log('')
  log('Please provide your Supabase Cloud project details:')
  log('(You can find these in your Supabase dashboard > Settings > API)')
  log('')

  const supabaseUrl = await question('Supabase Project URL (https://xxx.supabase.co): ')
  const anonKey = await question('Supabase Anon Key: ')
  const serviceKey = await question('Supabase Service Role Key: ')

  rl.close()

  // Validate inputs
  if (!supabaseUrl || !anonKey || !serviceKey) {
    throw new Error('All Supabase credentials are required')
  }

  if (!supabaseUrl.includes('supabase.co')) {
    throw new Error('Invalid Supabase URL format. Should be https://xxx.supabase.co')
  }

  return { supabaseUrl, anonKey, serviceKey }
}

function updateCustomerAppEnv(credentials) {
  const envPath = 'apps/customer-app/.env.local'
  log(`Updating customer app environment: ${envPath}`)
  
  createBackup(envPath)
  
  const content = `# ProcureServe II Customer App - Supabase Cloud Configuration
# Updated: ${new Date().toISOString()}
# Environment: Development (Supabase Cloud)

# Supabase Cloud Configuration
PUBLIC_SUPABASE_URL=${credentials.supabaseUrl}
PUBLIC_SUPABASE_ANON_KEY=${credentials.anonKey}
SUPABASE_SERVICE_ROLE_KEY=${credentials.serviceKey}

# App Configuration
PUBLIC_APP_URL=http://localhost:3004
PUBLIC_APP_PORT=3004

# Development Settings
NODE_ENV=development
DEBUG_AUTH=true

# Cloud Environment Flags
SUPABASE_ENVIRONMENT=cloud
SKIP_LOCAL_MIGRATIONS=true
`

  fs.writeFileSync(envPath, content)
  log(`✅ Updated ${envPath}`, 'success')
}

function updateConsoleAppEnv(credentials) {
  const envPath = 'apps/console-app/.env.local'
  log(`Updating console app environment: ${envPath}`)
  
  createBackup(envPath)
  
  const content = `# ProcureServe II Console App - Supabase Cloud Configuration
# Updated: ${new Date().toISOString()}
# Environment: Development (Supabase Cloud)

# Supabase Cloud Configuration
PUBLIC_SUPABASE_URL=${credentials.supabaseUrl}
PUBLIC_SUPABASE_ANON_KEY=${credentials.anonKey}
SUPABASE_SERVICE_ROLE_KEY=${credentials.serviceKey}

# App Configuration
PUBLIC_APP_URL=http://localhost:3008
PUBLIC_APP_PORT=3008

# Development Settings
NODE_ENV=development
DEBUG_AUTH=true

# Cloud Environment Flags
SUPABASE_ENVIRONMENT=cloud
SKIP_LOCAL_MIGRATIONS=true
`

  fs.writeFileSync(envPath, content)
  log(`✅ Updated ${envPath}`, 'success')
}

function updateSupabaseConfig(credentials) {
  const configPath = 'supabase/config.toml'
  
  if (fs.existsSync(configPath)) {
    createBackup(configPath)
    
    let content = fs.readFileSync(configPath, 'utf8')
    
    // Add cloud configuration section
    const cloudConfig = `
# Cloud Development Configuration
# This project uses Supabase Cloud for development
# Local Supabase is disabled to avoid migration conflicts

[cloud]
enabled = true
project_url = "${credentials.supabaseUrl}"
anon_key = "${credentials.anonKey}"

[local]
# Disabled for cloud development
enabled = false
`
    
    content += cloudConfig
    fs.writeFileSync(configPath, content)
    log(`✅ Updated ${configPath}`, 'success')
  }
}

function createCloudMigrationScript(credentials) {
  const scriptPath = 'scripts/run-cloud-migrations.js'
  
  const content = `#!/usr/bin/env node

/**
 * Cloud Migration Runner
 * Applies migrations to Supabase Cloud database
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

const SUPABASE_URL = '${credentials.supabaseUrl}'
const SUPABASE_SERVICE_KEY = '${credentials.serviceKey}'

async function runMigrations() {
  console.log('🌐 Running migrations on Supabase Cloud...')
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)
  
  // Get all migration files
  const migrationsDir = 'supabase/migrations'
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort()
  
  console.log(\`Found \${migrationFiles.length} migration files\`)
  
  for (const file of migrationFiles) {
    console.log(\`Running migration: \${file}\`)
    
    const migrationSQL = fs.readFileSync(path.join(migrationsDir, file), 'utf8')
    
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL })
      
      if (error) {
        console.error(\`❌ Migration failed: \${file}\`, error)
      } else {
        console.log(\`✅ Migration completed: \${file}\`)
      }
    } catch (err) {
      console.error(\`❌ Migration error: \${file}\`, err.message)
    }
  }
  
  console.log('🎉 All migrations completed!')
}

if (require.main === module) {
  runMigrations().catch(console.error)
}

module.exports = { runMigrations }
`

  fs.writeFileSync(scriptPath, content)
  fs.chmodSync(scriptPath, '755')
  log(`✅ Created cloud migration script: ${scriptPath}`, 'success')
}

function createCloudTestScript(credentials) {
  const scriptPath = 'scripts/test-cloud-connection.js'
  
  const content = `#!/usr/bin/env node

/**
 * Supabase Cloud Connection Test
 * Validates connection and basic functionality
 */

const { createClient } = require('@supabase/supabase-js')

const SUPABASE_URL = '${credentials.supabaseUrl}'
const SUPABASE_ANON_KEY = '${credentials.anonKey}'
const SUPABASE_SERVICE_KEY = '${credentials.serviceKey}'

async function testConnection() {
  console.log('🌐 Testing Supabase Cloud connection...')
  
  // Test anon client
  const anonClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
  
  try {
    const { data, error } = await anonClient.from('companies').select('count').limit(1)
    if (error) {
      console.log('⚠️ Anon client test (expected if no data):', error.message)
    } else {
      console.log('✅ Anon client connection successful')
    }
  } catch (err) {
    console.log('⚠️ Anon client test error:', err.message)
  }
  
  // Test service client
  const serviceClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)
  
  try {
    const { data, error } = await serviceClient.from('companies').select('count').limit(1)
    if (error) {
      console.log('⚠️ Service client test:', error.message)
    } else {
      console.log('✅ Service client connection successful')
    }
  } catch (err) {
    console.log('❌ Service client test error:', err.message)
  }
  
  // Test auth
  try {
    const { data: { user }, error } = await anonClient.auth.getUser()
    console.log('✅ Auth service accessible')
  } catch (err) {
    console.log('❌ Auth service error:', err.message)
  }
  
  console.log('🎉 Cloud connection test completed!')
}

if (require.main === module) {
  testConnection().catch(console.error)
}

module.exports = { testConnection }
`

  fs.writeFileSync(scriptPath, content)
  fs.chmodSync(scriptPath, '755')
  log(`✅ Created cloud test script: ${scriptPath}`, 'success')
}

function updatePackageJsonScripts() {
  const customerPackagePath = 'apps/customer-app/package.json'
  const consolePackagePath = 'apps/console-app/package.json'
  
  // Update customer app package.json
  if (fs.existsSync(customerPackagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(customerPackagePath, 'utf8'))
    
    packageJson.scripts = {
      ...packageJson.scripts,
      'dev:cloud': 'vite dev --port 3004',
      'test:cloud': 'node ../../scripts/test-cloud-connection.js',
      'migrate:cloud': 'node ../../scripts/run-cloud-migrations.js'
    }
    
    fs.writeFileSync(customerPackagePath, JSON.stringify(packageJson, null, 2))
    log(`✅ Updated customer app scripts`, 'success')
  }
  
  // Update console app package.json
  if (fs.existsSync(consolePackagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(consolePackagePath, 'utf8'))
    
    packageJson.scripts = {
      ...packageJson.scripts,
      'dev:cloud': 'vite dev --port 3008',
      'test:cloud': 'node ../../scripts/test-cloud-connection.js'
    }
    
    fs.writeFileSync(consolePackagePath, JSON.stringify(packageJson, null, 2))
    log(`✅ Updated console app scripts`, 'success')
  }
}

function createDevelopmentGuide() {
  const guidePath = 'docs/SUPABASE_CLOUD_DEVELOPMENT.md'
  
  const content = `# 🌐 Supabase Cloud Development Guide

## Overview

This project now uses **Supabase Cloud** for development to eliminate local migration issues and simplify the development workflow.

## Benefits

✅ **No Local Setup Required** - No Docker, no local Supabase instance
✅ **Consistent Environment** - Same database for all developers  
✅ **No Migration Conflicts** - Cloud handles migrations seamlessly
✅ **Faster Development** - Instant setup, no local resource usage
✅ **Real-time Features** - Full Supabase feature set available

## Quick Start

### 1. Environment Setup
The environment files have been configured automatically:
- \`apps/customer-app/.env.local\` - Customer app cloud config
- \`apps/console-app/.env.local\` - Console app cloud config

### 2. Test Connection
\`\`\`bash
npm run test:cloud
\`\`\`

### 3. Start Development
\`\`\`bash
# Customer app
cd apps/customer-app
npm run dev:cloud

# Console app (separate terminal)
cd apps/console-app  
npm run dev:cloud
\`\`\`

## Available Scripts

### Connection Testing
\`\`\`bash
npm run test:cloud          # Test Supabase Cloud connection
\`\`\`

### Migration Management
\`\`\`bash
npm run migrate:cloud       # Run migrations on cloud database
\`\`\`

### Development
\`\`\`bash
npm run dev:cloud           # Start development server with cloud config
\`\`\`

## Database Access

### Supabase Dashboard
- URL: [Supabase Dashboard](https://app.supabase.com)
- Access your project dashboard for:
  - Database browser
  - SQL editor
  - Authentication management
  - Real-time logs

### Direct Database Access
Use the SQL editor in Supabase dashboard for:
- Running custom queries
- Viewing table data
- Managing users
- Debugging issues

## Development Workflow

### 1. Daily Development
\`\`\`bash
# Start both apps
npm run dev:cloud
\`\`\`

### 2. Database Changes
- Use Supabase dashboard SQL editor
- Or create migration files and run \`npm run migrate:cloud\`

### 3. Testing
- All test users are available in cloud database
- Use existing test credentials
- No local setup required

## Troubleshooting

### Connection Issues
\`\`\`bash
# Test connection
npm run test:cloud

# Check environment variables
cat apps/customer-app/.env.local
\`\`\`

### Authentication Issues
- Check Supabase dashboard > Authentication
- Verify test users exist
- Check RLS policies in dashboard

### Migration Issues
- Use Supabase dashboard SQL editor
- Check migration logs in dashboard
- Run migrations manually if needed

## Security Notes

🔒 **Development Database**: This cloud instance is for development only
🔒 **Production Separation**: Use separate Supabase project for production
🔒 **API Keys**: Development keys are safe to commit (anon key only)
🔒 **Service Key**: Keep service role key secure, don't expose in client code

## Next Steps

1. ✅ Test cloud connection
2. ✅ Start development servers
3. ✅ Verify authentication flows
4. ✅ Test both customer and console apps
5. ✅ Continue feature development

## Support

If you encounter issues:
1. Check Supabase dashboard logs
2. Run connection test script
3. Verify environment configuration
4. Check this guide for troubleshooting steps

Happy coding! 🚀
`

  fs.writeFileSync(guidePath, content)
  log(`✅ Created development guide: ${guidePath}`, 'success')
}

async function main() {
  try {
    log('🌐 Setting up Supabase Cloud for ProcureServe II development...')
    log('')
    
    // Get cloud credentials
    const credentials = await promptForCloudCredentials()
    
    log('')
    log('📝 Updating configuration files...')
    
    // Update environment files
    updateCustomerAppEnv(credentials)
    updateConsoleAppEnv(credentials)
    
    // Update configuration
    updateSupabaseConfig(credentials)
    updatePackageJsonScripts()
    
    // Create helper scripts
    createCloudMigrationScript(credentials)
    createCloudTestScript(credentials)
    
    // Create documentation
    createDevelopmentGuide()
    
    log('')
    log('🎉 Supabase Cloud setup completed successfully!', 'success')
    log('')
    log('Next steps:')
    log('1. Test connection: npm run test:cloud')
    log('2. Start customer app: cd apps/customer-app && npm run dev:cloud')
    log('3. Start console app: cd apps/console-app && npm run dev:cloud')
    log('4. Access apps:')
    log('   - Customer App: http://localhost:3004')
    log('   - Console App: http://localhost:3008')
    log('')
    log('📖 See docs/SUPABASE_CLOUD_DEVELOPMENT.md for detailed guide')
    
  } catch (error) {
    log(`❌ Setup failed: ${error.message}`, 'error')
    process.exit(1)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export {
  updateCustomerAppEnv,
  updateConsoleAppEnv,
  createCloudTestScript
}
