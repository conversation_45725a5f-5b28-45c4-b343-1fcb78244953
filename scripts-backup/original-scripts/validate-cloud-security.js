#!/usr/bin/env node

/**
 * Supabase Cloud Security Validation
 * Validates security implementation with cloud database
 */

import fs from 'fs'
import readline from 'readline'

function log(message, type = 'info') {
  const timestamp = new Date().toISOString()
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
  console.log(`${prefix} [${timestamp}] ${message}`)
}

async function getCloudCredentials() {
  // Try to read from environment files first
  const customerEnvPath = 'apps/customer-app/.env.local'
  
  if (fs.existsSync(customerEnvPath)) {
    const envContent = fs.readFileSync(customerEnvPath, 'utf8')
    const urlMatch = envContent.match(/PUBLIC_SUPABASE_URL=(.+)/)
    const keyMatch = envContent.match(/PUBLIC_SUPABASE_ANON_KEY=(.+)/)
    const serviceMatch = envContent.match(/SUPABASE_SERVICE_ROLE_KEY=(.+)/)
    
    if (urlMatch && keyMatch && serviceMatch) {
      return {
        url: urlMatch[1].trim(),
        anonKey: keyMatch[1].trim(),
        serviceKey: serviceMatch[1].trim()
      }
    }
  }
  
  // If not found in env file, prompt user
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })

  const question = (prompt) => new Promise((resolve) => {
    rl.question(prompt, resolve)
  })

  log('Please provide your Supabase Cloud credentials:')
  const url = await question('Supabase URL: ')
  const anonKey = await question('Anon Key: ')
  const serviceKey = await question('Service Key: ')
  
  rl.close()
  
  return { url, anonKey, serviceKey }
}

class CloudSecurityValidator {
  constructor(credentials) {
    this.credentials = credentials
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      tests: []
    }
  }

  async runTest(name, testFn) {
    log(`Running test: ${name}`)
    try {
      const result = await testFn()
      if (result.success) {
        this.results.passed++
        log(`✅ PASSED: ${name}`, 'success')
      } else {
        this.results.failed++
        log(`❌ FAILED: ${name} - ${result.error}`, 'error')
      }
      this.results.tests.push({ name, ...result })
    } catch (error) {
      this.results.failed++
      log(`❌ ERROR: ${name} - ${error.message}`, 'error')
      this.results.tests.push({ name, success: false, error: error.message })
    }
  }

  async validateEnvironmentConfiguration() {
    return this.runTest('Environment Configuration', async () => {
      const issues = []
      
      // Check customer app .env.local
      const customerEnvPath = 'apps/customer-app/.env.local'
      if (fs.existsSync(customerEnvPath)) {
        const content = fs.readFileSync(customerEnvPath, 'utf8')
        if (!content.includes('supabase.co')) {
          issues.push('Customer app not configured for Supabase Cloud')
        }
        if (!content.includes('SUPABASE_ENVIRONMENT=cloud')) {
          issues.push('Customer app missing cloud environment flag')
        }
      } else {
        issues.push('Customer app .env.local missing')
      }
      
      // Check console app .env.local
      const consoleEnvPath = 'apps/console-app/.env.local'
      if (fs.existsSync(consoleEnvPath)) {
        const content = fs.readFileSync(consoleEnvPath, 'utf8')
        if (!content.includes('supabase.co')) {
          issues.push('Console app not configured for Supabase Cloud')
        }
      } else {
        issues.push('Console app .env.local missing')
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateCloudConnection() {
    return this.runTest('Supabase Cloud Connection', async () => {
      const issues = []
      
      try {
        // Import createClient
        const { createClient } = await import('@supabase/supabase-js')
        
        const supabase = createClient(this.credentials.url, this.credentials.anonKey)
        
        // Test basic connection
        const { data, error } = await supabase.from('companies').select('count').limit(1)
        
        if (error && !error.message.includes('permission denied')) {
          issues.push(`Connection failed: ${error.message}`)
        }
        
        // Test auth service
        const { data: authData, error: authError } = await supabase.auth.getSession()
        if (authError) {
          issues.push(`Auth service error: ${authError.message}`)
        }
        
      } catch (err) {
        issues.push(`Connection error: ${err.message}`)
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateSecurityUtilities() {
    return this.runTest('Security Utilities', async () => {
      const issues = []
      
      // Check if security utilities exist
      const authUtilsPath = 'apps/customer-app/src/lib/server/auth.ts'
      if (!fs.existsSync(authUtilsPath)) {
        issues.push('Auth utilities file missing')
      } else {
        const content = fs.readFileSync(authUtilsPath, 'utf8')
        const requiredFunctions = [
          'validateServerAuth',
          'getUserProfile', 
          'validateProcessPermission',
          'determineUserRedirect'
        ]
        
        requiredFunctions.forEach(func => {
          if (!content.includes(func)) {
            issues.push(`${func} function missing`)
          }
        })
      }
      
      // Check security config
      const securityConfigPath = 'apps/customer-app/src/lib/config/security.ts'
      if (!fs.existsSync(securityConfigPath)) {
        issues.push('Security config file missing')
      } else {
        const content = fs.readFileSync(securityConfigPath, 'utf8')
        if (!content.includes('SECURITY_CONFIG')) {
          issues.push('SECURITY_CONFIG missing')
        }
        if (!content.includes('SecurityValidator')) {
          issues.push('SecurityValidator class missing')
        }
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateAuthenticationFlow() {
    return this.runTest('Authentication Flow', async () => {
      const issues = []
      
      // Check hooks.server.ts
      const hooksPath = 'apps/customer-app/src/hooks.server.ts'
      if (fs.existsSync(hooksPath)) {
        const content = fs.readFileSync(hooksPath, 'utf8')
        
        if (!content.includes('getUser()')) {
          issues.push('hooks.server.ts not using secure getUser() method')
        }
        
        if (content.includes('getSession()') && !content.includes('// Get session for client-side compatibility')) {
          issues.push('hooks.server.ts may be using insecure getSession() for validation')
        }
        
        if (!content.includes('validateServerAuth') && !content.includes('getUserProfile')) {
          issues.push('hooks.server.ts not using centralized auth utilities')
        }
      } else {
        issues.push('hooks.server.ts missing')
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateDevelopmentScripts() {
    return this.runTest('Development Scripts', async () => {
      const issues = []
      
      // Check if cloud scripts exist
      const cloudTestScript = 'scripts/test-cloud-connection.js'
      if (!fs.existsSync(cloudTestScript)) {
        issues.push('Cloud connection test script missing')
      }
      
      const cloudMigrationScript = 'scripts/run-cloud-migrations.js'
      if (!fs.existsSync(cloudMigrationScript)) {
        issues.push('Cloud migration script missing')
      }
      
      // Check package.json scripts
      const customerPackagePath = 'apps/customer-app/package.json'
      if (fs.existsSync(customerPackagePath)) {
        const packageJson = JSON.parse(fs.readFileSync(customerPackagePath, 'utf8'))
        
        if (!packageJson.scripts['dev:cloud']) {
          issues.push('Customer app missing dev:cloud script')
        }
        if (!packageJson.scripts['test:cloud']) {
          issues.push('Customer app missing test:cloud script')
        }
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateDocumentation() {
    return this.runTest('Documentation', async () => {
      const issues = []
      
      const cloudGuide = 'docs/SUPABASE_CLOUD_DEVELOPMENT.md'
      if (!fs.existsSync(cloudGuide)) {
        issues.push('Supabase Cloud development guide missing')
      }
      
      const securityGuide = 'docs/SECURITY_BEST_PRACTICES_IMPLEMENTATION.md'
      if (!fs.existsSync(securityGuide)) {
        issues.push('Security best practices guide missing')
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async runAllTests() {
    log('🔒 Starting Supabase Cloud security validation...')
    
    await this.validateEnvironmentConfiguration()
    await this.validateCloudConnection()
    await this.validateSecurityUtilities()
    await this.validateAuthenticationFlow()
    await this.validateDevelopmentScripts()
    await this.validateDocumentation()
    
    this.printResults()
  }

  printResults() {
    log('')
    log('📊 SUPABASE CLOUD SECURITY VALIDATION RESULTS')
    log('==============================================')
    log(`✅ Passed: ${this.results.passed}`)
    log(`❌ Failed: ${this.results.failed}`)
    log(`⚠️ Warnings: ${this.results.warnings}`)
    log('')
    
    if (this.results.failed > 0) {
      log('❌ FAILED TESTS:')
      this.results.tests
        .filter(test => !test.success)
        .forEach(test => {
          log(`  - ${test.name}: ${test.error}`)
        })
      log('')
    }
    
    if (this.results.passed === this.results.tests.length) {
      log('🎉 ALL CLOUD SECURITY TESTS PASSED!', 'success')
      log('Your Supabase Cloud setup is secure and ready for development.')
    } else {
      log('⚠️ SOME ISSUES FOUND', 'warning')
      log('Please address the failed tests for optimal security.')
    }
    
    log('')
    log('Next steps:')
    log('1. Fix any failed tests')
    log('2. Run: npm run test:cloud')
    log('3. Start development: npm run dev:cloud')
    log('4. Test authentication flows')
  }
}

async function main() {
  try {
    log('🌐 Supabase Cloud Security Validation')
    log('====================================')
    
    const credentials = await getCloudCredentials()
    const validator = new CloudSecurityValidator(credentials)
    await validator.runAllTests()
    
  } catch (error) {
    log(`Fatal error: ${error.message}`, 'error')
    process.exit(1)
  }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { CloudSecurityValidator }
