#!/usr/bin/env node

/**
 * Security Validation Script
 * Validates all security fixes and best practices implementation
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Configuration
const SUPABASE_URL = 'http://127.0.0.1:54321'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

// Test users
const TEST_USERS = [
  { email: '<EMAIL>', password: 'password123', role: 'admin' },
  { email: '<EMAIL>', password: 'password123', role: 'manager' },
  { email: '<EMAIL>', password: 'password123', role: 'recruiter' }
]

function log(message, type = 'info') {
  const timestamp = new Date().toISOString()
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
  console.log(`${prefix} [${timestamp}] ${message}`)
}

class SecurityValidator {
  constructor() {
    this.supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
    this.adminSupabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      tests: []
    }
  }

  async runTest(name, testFn) {
    log(`Running test: ${name}`)
    try {
      const result = await testFn()
      if (result.success) {
        this.results.passed++
        log(`✅ PASSED: ${name}`, 'success')
      } else {
        this.results.failed++
        log(`❌ FAILED: ${name} - ${result.error}`, 'error')
      }
      this.results.tests.push({ name, ...result })
    } catch (error) {
      this.results.failed++
      log(`❌ ERROR: ${name} - ${error.message}`, 'error')
      this.results.tests.push({ name, success: false, error: error.message })
    }
  }

  async validateEnvironmentConfiguration() {
    return this.runTest('Environment Configuration', async () => {
      const issues = []
      
      // Check customer app .env.local
      const customerEnvPath = 'apps/customer-app/.env.local'
      if (fs.existsSync(customerEnvPath)) {
        const content = fs.readFileSync(customerEnvPath, 'utf8')
        if (!content.includes('http://127.0.0.1:54321')) {
          issues.push('Customer app has incorrect Supabase URL')
        }
        if (!content.includes('PUBLIC_APP_PORT=3004')) {
          issues.push('Customer app missing correct port configuration')
        }
      } else {
        issues.push('Customer app .env.local missing')
      }
      
      // Check console app .env.local
      const consoleEnvPath = 'apps/console-app/.env.local'
      if (fs.existsSync(consoleEnvPath)) {
        const content = fs.readFileSync(consoleEnvPath, 'utf8')
        if (!content.includes('http://127.0.0.1:54321')) {
          issues.push('Console app has incorrect Supabase URL')
        }
      } else {
        issues.push('Console app .env.local missing')
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateRLSPolicies() {
    return this.runTest('RLS Policies Enabled', async () => {
      const tables = ['companies', 'users', 'jobs', 'candidates', 'applications', 'consultants', 'projects', 'clients', 'placements']
      const issues = []
      
      for (const table of tables) {
        try {
          const { data, error } = await this.adminSupabase
            .rpc('check_rls_enabled', { table_name: table })
          
          if (error || !data) {
            // Fallback check using direct query
            const { data: rlsData, error: rlsError } = await this.adminSupabase
              .from('pg_class')
              .select('relrowsecurity')
              .eq('relname', table)
              .single()
            
            if (rlsError || !rlsData?.relrowsecurity) {
              issues.push(`RLS not enabled on ${table}`)
            }
          }
        } catch (err) {
          issues.push(`Could not verify RLS on ${table}: ${err.message}`)
        }
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { checkedTables: tables, issues }
      }
    })
  }

  async validateAuthenticationSecurity() {
    return this.runTest('Authentication Security', async () => {
      const issues = []
      
      // Test invalid credentials
      const { data: invalidAuth, error: invalidError } = await this.supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
      
      if (invalidAuth.user) {
        issues.push('Authentication allows invalid credentials')
      }
      
      // Test valid credentials
      const testUser = TEST_USERS[0]
      const { data: validAuth, error: validError } = await this.supabase.auth.signInWithPassword({
        email: testUser.email,
        password: testUser.password
      })
      
      if (validError || !validAuth.user) {
        issues.push('Valid authentication failed')
      } else {
        // Test user profile access
        const { data: userProfile, error: profileError } = await this.supabase
          .from('users')
          .select('*')
          .eq('id', validAuth.user.id)
          .single()
        
        if (profileError || !userProfile) {
          issues.push('User profile access failed')
        }
        
        // Sign out
        await this.supabase.auth.signOut()
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateDataIsolation() {
    return this.runTest('Data Isolation (RLS)', async () => {
      const issues = []
      
      // Sign in as test user
      const testUser = TEST_USERS[0]
      const { data: auth, error: authError } = await this.supabase.auth.signInWithPassword({
        email: testUser.email,
        password: testUser.password
      })
      
      if (authError || !auth.user) {
        return { success: false, error: 'Could not authenticate test user' }
      }
      
      try {
        // Test that user can only see their company's data
        const { data: companies, error: companiesError } = await this.supabase
          .from('companies')
          .select('*')
        
        if (companiesError) {
          issues.push(`Company access error: ${companiesError.message}`)
        } else if (companies.length > 1) {
          issues.push('User can see multiple companies (RLS violation)')
        }
        
        // Test that user can only see their company's users
        const { data: users, error: usersError } = await this.supabase
          .from('users')
          .select('*')
        
        if (usersError) {
          issues.push(`Users access error: ${usersError.message}`)
        } else {
          const uniqueCompanies = [...new Set(users.map(u => u.company_id))]
          if (uniqueCompanies.length > 1) {
            issues.push('User can see users from multiple companies (RLS violation)')
          }
        }
        
      } finally {
        await this.supabase.auth.signOut()
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateSecurityUtilities() {
    return this.runTest('Security Utilities', async () => {
      const issues = []
      
      // Check if security utilities exist
      const authUtilsPath = 'apps/customer-app/src/lib/server/auth.ts'
      if (!fs.existsSync(authUtilsPath)) {
        issues.push('Auth utilities file missing')
      } else {
        const content = fs.readFileSync(authUtilsPath, 'utf8')
        if (!content.includes('validateServerAuth')) {
          issues.push('validateServerAuth function missing')
        }
        if (!content.includes('getUserProfile')) {
          issues.push('getUserProfile function missing')
        }
        if (!content.includes('validateProcessPermission')) {
          issues.push('validateProcessPermission function missing')
        }
      }
      
      // Check security config
      const securityConfigPath = 'apps/customer-app/src/lib/config/security.ts'
      if (!fs.existsSync(securityConfigPath)) {
        issues.push('Security config file missing')
      } else {
        const content = fs.readFileSync(securityConfigPath, 'utf8')
        if (!content.includes('SECURITY_CONFIG')) {
          issues.push('SECURITY_CONFIG missing')
        }
        if (!content.includes('SecurityValidator')) {
          issues.push('SecurityValidator class missing')
        }
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateProcessPermissions() {
    return this.runTest('Process Permissions', async () => {
      const issues = []
      
      // Test each user type
      for (const testUser of TEST_USERS) {
        const { data: auth, error: authError } = await this.supabase.auth.signInWithPassword({
          email: testUser.email,
          password: testUser.password
        })
        
        if (authError || !auth.user) {
          issues.push(`Could not authenticate ${testUser.email}`)
          continue
        }
        
        try {
          // Get user profile
          const { data: userProfile, error: profileError } = await this.supabase
            .from('users')
            .select('process_permissions, profile')
            .eq('id', auth.user.id)
            .single()
          
          if (profileError || !userProfile) {
            issues.push(`Could not get profile for ${testUser.email}`)
            continue
          }
          
          // Check permissions structure
          const permissions = userProfile.profile?.process_permissions || userProfile.process_permissions || []
          if (!Array.isArray(permissions)) {
            issues.push(`Invalid permissions structure for ${testUser.email}`)
          }
          
          // Validate permission values
          const validPermissions = ['recruitment', 'bench_sales']
          const invalidPerms = permissions.filter(p => !validPermissions.includes(p))
          if (invalidPerms.length > 0) {
            issues.push(`Invalid permissions for ${testUser.email}: ${invalidPerms.join(', ')}`)
          }
          
        } finally {
          await this.supabase.auth.signOut()
        }
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async validateSecurityHeaders() {
    return this.runTest('Security Headers Implementation', async () => {
      const issues = []
      
      // Check if security middleware exists
      const hooksPath = 'apps/customer-app/src/hooks.server.ts'
      if (fs.existsSync(hooksPath)) {
        const content = fs.readFileSync(hooksPath, 'utf8')
        if (!content.includes('getUser()')) {
          issues.push('hooks.server.ts not using secure getUser() method')
        }
        if (content.includes('getSession()') && !content.includes('getUser()')) {
          issues.push('hooks.server.ts still using insecure getSession() only')
        }
      } else {
        issues.push('hooks.server.ts missing')
      }
      
      return {
        success: issues.length === 0,
        error: issues.join(', '),
        details: { issues }
      }
    })
  }

  async runAllTests() {
    log('🔒 Starting comprehensive security validation...')
    
    await this.validateEnvironmentConfiguration()
    await this.validateRLSPolicies()
    await this.validateAuthenticationSecurity()
    await this.validateDataIsolation()
    await this.validateSecurityUtilities()
    await this.validateProcessPermissions()
    await this.validateSecurityHeaders()
    
    this.printResults()
  }

  printResults() {
    log('')
    log('📊 SECURITY VALIDATION RESULTS')
    log('================================')
    log(`✅ Passed: ${this.results.passed}`)
    log(`❌ Failed: ${this.results.failed}`)
    log(`⚠️ Warnings: ${this.results.warnings}`)
    log('')
    
    if (this.results.failed > 0) {
      log('❌ FAILED TESTS:')
      this.results.tests
        .filter(test => !test.success)
        .forEach(test => {
          log(`  - ${test.name}: ${test.error}`)
        })
      log('')
    }
    
    if (this.results.passed === this.results.tests.length) {
      log('🎉 ALL SECURITY TESTS PASSED!', 'success')
      log('The application meets security best practices.')
    } else {
      log('⚠️ SECURITY ISSUES FOUND', 'warning')
      log('Please address the failed tests before proceeding to production.')
    }
    
    log('')
    log('Next steps:')
    log('1. Fix any failed security tests')
    log('2. Run end-to-end tests')
    log('3. Perform manual security review')
    log('4. Deploy to staging environment')
  }
}

async function main() {
  const validator = new SecurityValidator()
  await validator.runAllTests()
}

if (require.main === module) {
  main().catch(error => {
    log(`Fatal error: ${error.message}`, 'error')
    process.exit(1)
  })
}

module.exports = { SecurityValidator }
