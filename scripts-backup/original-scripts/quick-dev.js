#!/usr/bin/env node

/**
 * PSII Quick Dev Script
 * Quick start for individual app development with automatic port management
 */

import { spawn, execSync } from 'child_process'
import PortManager from './port-manager.js'

const COLORS = {
  RED: '\x1b[31m',
  G<PERSON><PERSON>: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  RESET: '\x1b[0m',
  BOLD: '\x1b[1m'
}

class QuickDev {
  constructor() {
    this.portManager = new PortManager()
  }

  log(message, color = COLORS.CYAN) {
    console.log(`${color}[QUICK-DEV]${COLORS.RESET} ${message}`)
  }

  error(message) {
    console.log(`${COLORS.RED}[ERROR]${COLORS.RESET} ${message}`)
  }

  success(message) {
    console.log(`${COLORS.GREEN}[SUCCESS]${COLORS.RESET} ${message}`)
  }

  async startSupabaseIfNeeded() {
    this.log('Checking Supabase services...')
    
    const supabaseRunning = await this.portManager.isPortInUse(54331)
    if (!supabaseRunning) {
      this.log('Starting Supabase services...')
      
      // Resolve any port conflicts first
      const conflicts = await this.portManager.checkPortConflicts()
      if (conflicts.length > 0) {
        await this.portManager.resolvePortConflicts(conflicts, true)
      }
      
      try {
        execSync('npx supabase start', { stdio: 'inherit' })
        this.success('Supabase services started')
      } catch (error) {
        this.error('Failed to start Supabase')
        process.exit(1)
      }
    } else {
      this.success('Supabase is already running')
    }
  }

  async startApp(appName) {
    const apps = {
      'customer': {
        name: 'Customer App',
        port: 3004,
        path: 'apps/customer-app',
        url: 'http://localhost:3004'
      },
      'console': {
        name: 'Console App', 
        port: 3005,
        path: 'apps/console-app',
        url: 'http://localhost:3005'
      }
    }

    const app = apps[appName]
    if (!app) {
      this.error(`Unknown app: ${appName}. Available: customer, console`)
      process.exit(1)
    }

    this.log(`Starting ${app.name}...`)

    // Check if port is available, kill if needed
    const portInUse = await this.portManager.isPortInUse(app.port)
    if (portInUse) {
      this.log(`Port ${app.port} is in use. Killing process...`)
      await this.portManager.killProcessOnPort(app.port)
      // Wait a moment for port to be freed
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    // Start the app
    const appProcess = spawn('npm', ['run', 'dev'], {
      cwd: app.path,
      stdio: 'inherit'
    })

    // Handle cleanup
    const cleanup = () => {
      this.log('Shutting down...')
      appProcess.kill()
      process.exit(0)
    }

    process.on('SIGINT', cleanup)
    process.on('SIGTERM', cleanup)

    // Wait for app to start
    this.log('Waiting for app to start...')
    for (let i = 0; i < 30; i++) {
      await new Promise(resolve => setTimeout(resolve, 2000))
      try {
        const response = await fetch(app.url)
        if (response.ok || response.status === 404) {
          this.success(`${app.name} is ready!`)
          console.log(`\n${COLORS.BOLD}${COLORS.GREEN}🚀 Development URLs:${COLORS.RESET}`)
          console.log(`  ${app.name}: ${COLORS.WHITE}${app.url}${COLORS.RESET}`)
          console.log(`  Supabase Studio: ${COLORS.WHITE}http://127.0.0.1:54333${COLORS.RESET}`)
          console.log(`\n${COLORS.YELLOW}Press Ctrl+C to stop${COLORS.RESET}\n`)
          break
        }
      } catch (error) {
        // Still starting up
      }
      
      if (i === 29) {
        this.error(`${app.name} failed to start within 60 seconds`)
        process.exit(1)
      }
    }

    // Keep running
    appProcess.on('close', (code) => {
      if (code !== 0) {
        this.error(`${app.name} exited with code ${code}`)
      }
      process.exit(code)
    })
  }

  async startBoth() {
    this.log('Starting both applications...')
    
    // Start Supabase first
    await this.startSupabaseIfNeeded()
    
    // Check and resolve port conflicts for both apps
    const customerPortInUse = await this.portManager.isPortInUse(3004)
    const consolePortInUse = await this.portManager.isPortInUse(3005)
    
    if (customerPortInUse) {
      this.log('Port 3004 is in use. Killing process...')
      await this.portManager.killProcessOnPort(3004)
    }
    
    if (consolePortInUse) {
      this.log('Port 3005 is in use. Killing process...')
      await this.portManager.killProcessOnPort(3005)
    }
    
    // Wait for ports to be freed
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Start both apps
    const customerProcess = spawn('npm', ['run', 'dev'], {
      cwd: 'apps/customer-app',
      stdio: 'pipe'
    })
    
    const consoleProcess = spawn('npm', ['run', 'dev'], {
      cwd: 'apps/console-app', 
      stdio: 'pipe'
    })

    // Handle cleanup
    const cleanup = () => {
      this.log('Shutting down both applications...')
      customerProcess.kill()
      consoleProcess.kill()
      process.exit(0)
    }

    process.on('SIGINT', cleanup)
    process.on('SIGTERM', cleanup)

    // Wait for both apps to start
    this.log('Waiting for applications to start...')
    
    let customerReady = false
    let consoleReady = false
    
    for (let i = 0; i < 30; i++) {
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      if (!customerReady) {
        try {
          const response = await fetch('http://localhost:3004')
          if (response.ok || response.status === 404) {
            customerReady = true
            this.success('Customer App is ready!')
          }
        } catch (error) {
          // Still starting
        }
      }
      
      if (!consoleReady) {
        try {
          const response = await fetch('http://localhost:3005')
          if (response.ok || response.status === 404) {
            consoleReady = true
            this.success('Console App is ready!')
          }
        } catch (error) {
          // Still starting
        }
      }
      
      if (customerReady && consoleReady) {
        console.log(`\n${COLORS.BOLD}${COLORS.GREEN}🚀 All Services Ready!${COLORS.RESET}`)
        console.log(`  Customer App: ${COLORS.WHITE}http://localhost:3004${COLORS.RESET}`)
        console.log(`  Console App: ${COLORS.WHITE}http://localhost:3005${COLORS.RESET}`)
        console.log(`  Supabase Studio: ${COLORS.WHITE}http://127.0.0.1:54333${COLORS.RESET}`)
        console.log(`\n${COLORS.YELLOW}Press Ctrl+C to stop all services${COLORS.RESET}\n`)
        break
      }
      
      if (i === 29) {
        this.error('One or more applications failed to start within 60 seconds')
        cleanup()
      }
    }

    // Keep running and handle process exits
    let processesRunning = 2
    
    customerProcess.on('close', (code) => {
      this.error(`Customer App exited with code ${code}`)
      processesRunning--
      if (processesRunning === 0) process.exit(1)
    })
    
    consoleProcess.on('close', (code) => {
      this.error(`Console App exited with code ${code}`)
      processesRunning--
      if (processesRunning === 0) process.exit(1)
    })
  }
}

async function main() {
  const args = process.argv.slice(2)
  const command = args[0]
  
  const quickDev = new QuickDev()

  if (!command || command === '--help' || command === '-h') {
    console.log(`
${COLORS.BOLD}PSII Quick Dev${COLORS.RESET}

Usage: node quick-dev.js <command>

Commands:
  customer    Start Customer App only (with Supabase)
  console     Start Console App only (with Supabase)  
  both        Start both applications (with Supabase)
  supabase    Start Supabase services only

Examples:
  node scripts/quick-dev.js customer
  node scripts/quick-dev.js console
  node scripts/quick-dev.js both
`)
    return
  }

  try {
    switch (command) {
      case 'customer':
        await quickDev.startSupabaseIfNeeded()
        await quickDev.startApp('customer')
        break
        
      case 'console':
        await quickDev.startSupabaseIfNeeded()
        await quickDev.startApp('console')
        break
        
      case 'both':
        await quickDev.startBoth()
        break
        
      case 'supabase':
        await quickDev.startSupabaseIfNeeded()
        quickDev.success('Supabase services are running')
        console.log(`  Studio: ${COLORS.WHITE}http://127.0.0.1:54333${COLORS.RESET}`)
        break
        
      default:
        quickDev.error(`Unknown command: ${command}`)
        process.exit(1)
    }
  } catch (error) {
    quickDev.error(`Command failed: ${error.message}`)
    process.exit(1)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
