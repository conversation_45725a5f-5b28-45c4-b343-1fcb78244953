const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createSampleBusinessUnits() {
  console.log('🏢 Creating Sample Business Units for Testing')
  console.log('=============================================')
  
  try {
    // Get the first company for testing
    const { data: companies } = await supabase
      .from('companies')
      .select('id, name')
      .limit(1)

    if (!companies || companies.length === 0) {
      console.log('❌ No companies found. Please ensure test data exists.')
      return
    }

    const companyId = companies[0].id
    console.log(`✅ Using company: ${companies[0].name} (${companyId})`)

    // Get an admin user to assign as manager
    const { data: adminUsers } = await supabase
      .from('users')
      .select('id, email')
      .eq('company_id', companyId)
      .eq('role', 'admin')
      .limit(1)

    const managerId = adminUsers?.[0]?.id

    // Create sample business units
    const businessUnits = [
      {
        company_id: companyId,
        name: 'Engineering',
        description: 'Software development and technical operations',
        manager_id: managerId,
        parent_id: null,
        settings: {}
      },
      {
        company_id: companyId,
        name: 'Sales & Marketing',
        description: 'Revenue generation and customer acquisition',
        manager_id: managerId,
        parent_id: null,
        settings: {}
      },
      {
        company_id: companyId,
        name: 'Human Resources',
        description: 'Talent acquisition and employee management',
        manager_id: managerId,
        parent_id: null,
        settings: {}
      }
    ]

    // Insert business units
    const { data: createdUnits, error: createError } = await supabase
      .from('business_units')
      .insert(businessUnits)
      .select()

    if (createError) {
      console.error('❌ Error creating business units:', createError)
      return
    }

    console.log(`✅ Created ${createdUnits.length} business units:`)
    createdUnits.forEach(unit => {
      console.log(`  - ${unit.name}: ${unit.description}`)
    })

    // Create sub-units
    const engineeringUnit = createdUnits.find(u => u.name === 'Engineering')
    const salesUnit = createdUnits.find(u => u.name === 'Sales & Marketing')

    if (engineeringUnit && salesUnit) {
      const subUnits = [
        {
          company_id: companyId,
          name: 'Frontend Development',
          description: 'User interface and experience development',
          parent_id: engineeringUnit.id,
          manager_id: null,
          settings: {}
        },
        {
          company_id: companyId,
          name: 'Backend Development',
          description: 'Server-side development and APIs',
          parent_id: engineeringUnit.id,
          manager_id: null,
          settings: {}
        },
        {
          company_id: companyId,
          name: 'Business Development',
          description: 'Partnership and growth initiatives',
          parent_id: salesUnit.id,
          manager_id: null,
          settings: {}
        }
      ]

      const { data: subUnitsCreated, error: subError } = await supabase
        .from('business_units')
        .insert(subUnits)
        .select()

      if (subError) {
        console.error('❌ Error creating sub-units:', subError)
      } else {
        console.log(`✅ Created ${subUnitsCreated.length} sub-units:`)
        subUnitsCreated.forEach(unit => {
          console.log(`  - ${unit.name} (under ${unit.parent_id === engineeringUnit.id ? 'Engineering' : 'Sales & Marketing'})`)
        })
      }
    }

    // Create some audit logs to populate the audit trail
    const auditLogs = [
      {
        company_id: companyId,
        performed_by: managerId,
        action_type: 'business_unit_created',
        target_user_id: null,
        old_values: null,
        new_values: { message: 'Sample business units created for testing' },
        ip_address: '127.0.0.1',
        user_agent: 'Test Script'
      }
    ]

    await supabase.from('company_audit_logs').insert(auditLogs)

    console.log('\n🎯 Test Data Summary:')
    console.log('  ✅ 3 top-level business units')
    console.log('  ✅ 3 sub-units for hierarchy testing') 
    console.log('  ✅ Manager assignments where possible')
    console.log('  ✅ Audit log entries created')
    console.log('\n🔗 Ready to test at:')
    console.log('  - Business Units: /settings/business-units')
    console.log('  - User Management: /settings/users')
    console.log('  - Audit Trail: /settings/audit')

  } catch (error) {
    console.error('❌ Script failed:', error)
  }
}

createSampleBusinessUnits()
