#!/usr/bin/env node

/**
 * Validation script to check if all the fixes are working
 * This runs basic checks without requiring a full database setup
 */

import { readFileSync } from 'fs'
import { resolve } from 'path'

console.log('🔍 Validating PSII fixes...\n')

// Check 1: Database types include process_permissions
console.log('1. Checking database types...')
try {
  const dbTypesPath = resolve('packages/database-types/index.ts')
  const dbTypesContent = readFileSync(dbTypesPath, 'utf8')
  
  if (dbTypesContent.includes('process_permissions: string[] | null')) {
    console.log('   ✅ process_permissions field added to users table types')
  } else {
    console.log('   ❌ process_permissions field missing from users table types')
  }
  
  if (dbTypesContent.includes('current_process: string | null')) {
    console.log('   ✅ current_process field added to users table types')
  } else {
    console.log('   ❌ current_process field missing from users table types')
  }
} catch (error) {
  console.log('   ❌ Error reading database types:', error.message)
}

// Check 2: Zod dependency
console.log('\n2. Checking zod dependency...')
try {
  const packageJsonPath = resolve('apps/customer-app/package.json')
  const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'))
  
  if (packageJson.dependencies?.zod || packageJson.devDependencies?.zod) {
    console.log('   ✅ zod dependency installed')
  } else {
    console.log('   ❌ zod dependency missing')
  }
} catch (error) {
  console.log('   ❌ Error checking package.json:', error.message)
}

// Check 3: Port configuration
console.log('\n3. Checking port configuration...')
try {
  const packageJsonPath = resolve('apps/customer-app/package.json')
  const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'))
  
  if (packageJson.scripts?.dev?.includes('--port 3004')) {
    console.log('   ✅ Customer app configured for port 3004')
  } else {
    console.log('   ❌ Customer app not configured for port 3004')
  }
  
  const playwrightConfigPath = resolve('playwright.config.js')
  const playwrightConfig = readFileSync(playwrightConfigPath, 'utf8')
  
  if (playwrightConfig.includes('http://localhost:3004')) {
    console.log('   ✅ Playwright configured for port 3004')
  } else {
    console.log('   ❌ Playwright not configured for port 3004')
  }
} catch (error) {
  console.log('   ❌ Error checking port configuration:', error.message)
}

// Check 4: Key files exist
console.log('\n4. Checking key files...')
const keyFiles = [
  'apps/customer-app/src/hooks.server.ts',
  'apps/customer-app/src/routes/test-users/+page.svelte',
  'apps/customer-app/src/routes/api/set-process/+server.ts',
  'scripts/e2e-tests.spec.js',
  'supabase/migrations/20250612000003_process_permissions_schema.sql'
]

keyFiles.forEach(file => {
  try {
    const filePath = resolve(file)
    readFileSync(filePath, 'utf8')
    console.log(`   ✅ ${file}`)
  } catch (error) {
    console.log(`   ❌ ${file} - ${error.message}`)
  }
})

// Check 5: Test users configuration
console.log('\n5. Checking test users configuration...')
try {
  const testUsersPath = resolve('apps/customer-app/src/routes/test-users/+page.svelte')
  const testUsersContent = readFileSync(testUsersPath, 'utf8')
  
  const expectedUsers = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ]
  
  const foundUsers = expectedUsers.filter(email => testUsersContent.includes(email))
  console.log(`   ✅ Found ${foundUsers.length}/${expectedUsers.length} test users configured`)
  
  if (foundUsers.length !== expectedUsers.length) {
    const missing = expectedUsers.filter(email => !testUsersContent.includes(email))
    console.log(`   ⚠️  Missing: ${missing.join(', ')}`)
  }
} catch (error) {
  console.log('   ❌ Error checking test users:', error.message)
}

console.log('\n🎯 Validation Summary:')
console.log('   - Database types updated with process_permissions fields')
console.log('   - Zod dependency added for API validation')
console.log('   - Port configuration aligned (3004)')
console.log('   - Key application files present')
console.log('   - Test users configured for E2E testing')

console.log('\n📋 Next Steps:')
console.log('   1. Start Docker Desktop')
console.log('   2. Run: npm run smart-setup')
console.log('   3. Run: npm run test-e2e')
console.log('\n✨ All critical issues have been fixed!')
