# Debug Scripts

This directory contains debugging and testing scripts used during development. These scripts are for temporary debugging and can be safely removed or modified as needed.

## Categories

### Authentication & User Management
- `check-auth-users.js` - Check relationship between auth users and console users
- `create-auth-users.cjs` - Create authentication users
- `create-console-auth-users.js` - Create console authentication users
- `create-console-users.js` - Create console users
- `create-customer-users.cjs` - Create customer application users
- `debug-console-auth.js` - Debug console authentication issues
- `fix-console-auth.cjs` - Fix console authentication problems
- `reset-admin-password.js` - Reset admin password
- `reset-console-password.js` - Reset console password
- `setup-auth-users.js` - Setup authentication users

### Company & Business Unit Management
- `check-companies.cjs` - Check company data
- `create-sample-business-units.cjs` - Create sample business units
- `check-and-create-units.cjs` - Check and create units if needed
- `check-units-service-role.cjs` - Check units with service role

### User Permissions & Access
- `check-user-permissions.cjs` - Check user permissions
- `check-and-fix-users.cjs` - Check and fix user issues
- `fix-user-linkage.cjs` - Fix user linkage issues
- `fix-customer-profiles.cjs` - Fix customer profile issues

### Testing & Verification
- `test-*.cjs/js` - Various test scripts for different features
- `verify-*.cjs` - Verification scripts for data and functionality
- `manual-verification-phase5.cjs` - Manual verification for phase 5

### Data Setup
- `create-test-data.cjs` - Create test data
- `create-bench-sales-data.cjs` - Create bench sales test data
- `setup-phase7-test-data.cjs` - Setup test data for phase 7

### Company Settings
- `debug-company-settings.cjs` - Debug company settings
- `test-company-settings.cjs` - Test company settings functionality

### Utilities
- `clear-auth.html` - HTML utility to clear authentication
- `quick-health-check.cjs` - Quick health check of the system
- `update-all-references.cjs` - Update all references script

## Usage

These scripts are temporary debugging tools. They can be run individually using Node.js:

```bash
node debug/script-name.cjs
# or
node debug/script-name.js
```

## Note

These scripts contain hardcoded Supabase URLs and keys for local development. Do not use in production.
