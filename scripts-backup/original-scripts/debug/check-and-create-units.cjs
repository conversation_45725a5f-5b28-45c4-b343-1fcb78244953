const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkTestData() {
  console.log('🔍 Checking Test Data')
  
  // Check companies
  const { data: companies } = await supabase.from('companies').select('*')
  console.log('Companies:', companies?.map(c => ({ id: c.id, name: c.name })))
  
  // Check users  
  const { data: users } = await supabase.from('users').select('id, email, company_id, role')
  console.log('Users:', users?.map(u => ({ email: u.email, company_id: u.company_id, role: u.role })))
  
  // Check business units with proper company
  if (users && users.length > 0) {
    const companyId = users[0].company_id
    console.log(`\nUsing company ID: ${companyId}`)
    
    // Create business units for the correct company
    const businessUnits = [
      {
        company_id: companyId,
        name: 'Engineering',
        description: 'Software development and technical operations',
        manager_id: users.find(u => u.role === 'admin')?.id,
        parent_id: null,
        settings: {}
      },
      {
        company_id: companyId,
        name: 'Sales & Marketing', 
        description: 'Revenue generation and customer acquisition',
        manager_id: null,
        parent_id: null,
        settings: {}
      }
    ]
    
    const { data: created, error } = await supabase
      .from('business_units')
      .upsert(businessUnits, { onConflict: 'company_id,name' })
      .select()
    
    if (error) {
      console.error('Error creating units:', error)
    } else {
      console.log('✅ Created business units:', created?.map(u => u.name))
    }
  }
}

checkTestData()
