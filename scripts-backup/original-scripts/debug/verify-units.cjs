const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function verifyBusinessUnits() {
  console.log('🏢 Verifying Business Units')
  
  const { data: units, error } = await supabase
    .from('business_units')
    .select('id, name, description, parent_id, manager_id')
    .order('name')

  if (error) {
    console.error('❌ Error:', error)
  } else {
    console.log(`✅ Found ${units.length} business units:`)
    units.forEach(unit => {
      const hierarchy = unit.parent_id ? '  └─ ' : '• '
      console.log(`${hierarchy}${unit.name}${unit.description ? ': ' + unit.description : ''}`)
    })
  }

  // Check audit logs
  const { data: logs } = await supabase
    .from('company_audit_logs')
    .select('action_type, created_at')
    .order('created_at', { ascending: false })
    .limit(3)

  console.log(`\n📋 Recent audit entries: ${logs?.length || 0}`)
}

verifyBusinessUnits()
