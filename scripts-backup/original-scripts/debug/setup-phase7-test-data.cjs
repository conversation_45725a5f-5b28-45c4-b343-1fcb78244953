const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createTestBusinessUnits() {
  console.log('🏢 Creating Test Business Units')
  
  // Use Acme Staffing company (the main test company)
  const companyId = '99e1f1a1-1111-1111-1111-111111111111'
  const adminUserId = (await supabase
    .from('users')
    .select('id')
    .eq('email', '<EMAIL>')
    .single()).data?.id

  // Delete existing units first
  await supabase.from('business_units').delete().eq('company_id', companyId)
  
  const businessUnits = [
    {
      company_id: companyId,
      name: 'Engineering',
      description: 'Software development and technical operations',
      manager_id: adminUserId,
      parent_id: null,
      settings: {}
    },
    {
      company_id: companyId,
      name: 'Sales & Marketing',
      description: 'Revenue generation and customer acquisition', 
      manager_id: adminUserId,
      parent_id: null,
      settings: {}
    },
    {
      company_id: companyId,
      name: 'Human Resources',
      description: 'Talent acquisition and employee management',
      manager_id: adminUserId,
      parent_id: null,
      settings: {}
    }
  ]
  
  const { data: created, error } = await supabase
    .from('business_units')
    .insert(businessUnits)
    .select()
  
  if (error) {
    console.error('❌ Error creating units:', error)
  } else {
    console.log('✅ Created business units:')
    created.forEach(unit => console.log(`  - ${unit.name}: ${unit.description}`))
    
    // Create sub-units
    const engineeringUnit = created.find(u => u.name === 'Engineering')
    const salesUnit = created.find(u => u.name === 'Sales & Marketing')
    
    const subUnits = [
      {
        company_id: companyId,
        name: 'Frontend Development',
        description: 'User interface development',
        parent_id: engineeringUnit.id,
        manager_id: null,
        settings: {}
      },
      {
        company_id: companyId,
        name: 'Backend Development', 
        description: 'Server-side development',
        parent_id: engineeringUnit.id,
        manager_id: null,
        settings: {}
      },
      {
        company_id: companyId,
        name: 'Business Development',
        description: 'Partnership initiatives',
        parent_id: salesUnit.id,
        manager_id: null,
        settings: {}
      }
    ]
    
    const { data: subCreated, error: subError } = await supabase
      .from('business_units')
      .insert(subUnits)
      .select()
      
    if (subError) {
      console.error('❌ Error creating sub-units:', subError)
    } else {
      console.log('✅ Created sub-units:')
      subCreated.forEach(unit => console.log(`  - ${unit.name}`))
    }
    
    // Create audit log
    await supabase.from('company_audit_logs').insert({
      company_id: companyId,
      performed_by: adminUserId,
      action_type: 'business_unit_created',
      target_user_id: null,
      old_values: null,
      new_values: { message: 'Test business units created' },
      ip_address: '127.0.0.1',
      user_agent: 'Setup Script'
    })
    
    console.log('\n🎯 Phase 7 Test Data Ready!')
    console.log('<NAME_EMAIL> / password123')
    console.log('Navigate to /settings to access all Phase 7 features')
  }
}

createTestBusinessUnits()
