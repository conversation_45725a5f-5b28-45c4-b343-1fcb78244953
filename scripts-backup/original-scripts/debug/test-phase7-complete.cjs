const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testPhase7Complete() {
  console.log('🚀 Testing Phase 7A, B, C Implementation')
  console.log('=====================================\n')
  
  try {
    // Test Phase 7A: User Edit Interface
    console.log('🔧 Phase 7A: User Edit Interface')
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select(`
        id, email, role, process_permissions, is_active,
        profile,
        business_unit:business_units!users_business_unit_id_fkey(id, name)
      `)
      .limit(3)

    if (usersError) {
      console.error('❌ Users fetch error:', usersError)
    } else {
      console.log('✅ User Edit Interface Ready')
      console.log(`  📊 ${users.length} users available for editing`)
      users.forEach(user => {
        console.log(`    - ${user.email} (${user.role}) - ${user.is_active ? 'Active' : 'Inactive'}`)
      })
    }

    // Test Phase 7B: Business Units
    console.log('\n🏢 Phase 7B: Business Units & Departments')
    const { data: businessUnits, error: unitsError } = await supabase
      .from('business_units')
      .select(`
        *,
        manager:users!business_units_manager_id_fkey(id, email, profile),
        parent_unit:business_units!business_units_parent_id_fkey(id, name)
      `)
      .limit(5)

    if (unitsError) {
      console.error('❌ Business units fetch error:', unitsError)
    } else {
      console.log('✅ Business Units Management Ready')
      console.log(`  📊 ${businessUnits.length} business units configured`)
      if (businessUnits.length > 0) {
        businessUnits.forEach(unit => {
          const managerName = unit.manager ? 
            (unit.manager.profile?.first_name || unit.manager.email.split('@')[0]) : 
            'No manager'
          console.log(`    - ${unit.name}: ${managerName}`)
        })
      } else {
        console.log('    💡 Ready to create first business unit via interface')
      }
    }

    // Test Phase 7C: Audit Trail
    console.log('\n📋 Phase 7C: Audit Trail Dashboard')
    const { data: auditLogs, error: auditError } = await supabase
      .from('company_audit_logs')
      .select(`
        *,
        performed_by_user:users!company_audit_logs_performed_by_fkey(id, email, profile),
        target_user:users!company_audit_logs_target_user_id_fkey(id, email, profile)
      `)
      .order('created_at', { ascending: false })
      .limit(5)

    if (auditError) {
      console.error('❌ Audit logs fetch error:', auditError)
    } else {
      console.log('✅ Audit Trail Dashboard Ready')
      console.log(`  📊 ${auditLogs.length} recent audit entries`)
      if (auditLogs.length > 0) {
        console.log('  📝 Recent activities:')
        auditLogs.forEach(log => {
          const userName = log.performed_by_user?.email?.split('@')[0] || 'Unknown'
          const actionType = log.action_type.replace(/_/g, ' ')
          const timeAgo = Math.floor((Date.now() - new Date(log.created_at).getTime()) / (1000 * 60))
          console.log(`    - ${userName}: ${actionType} (${timeAgo}m ago)`)
        })
      } else {
        console.log('    💡 Audit logging active - entries will appear as users perform actions')
      }
    }

    // Test database schema completeness
    console.log('\n🗄️  Database Schema Validation')
    
    // Check if all required tables exist
    const requiredTables = [
      'users', 'business_units', 'company_audit_logs', 
      'user_invitations', 'companies'
    ]
    
    for (const table of requiredTables) {
      try {
        const { error } = await supabase.from(table).select('id').limit(1)
        if (error) {
          console.log(`❌ Table ${table}: ${error.message}`)
        } else {
          console.log(`✅ Table ${table}: Available`)
        }
      } catch (e) {
        console.log(`❌ Table ${table}: Error - ${e.message}`)
      }
    }

    // Summary
    console.log('\n🎯 Phase 7 Implementation Summary')
    console.log('================================')
    console.log('✅ Phase 7A: User Edit Interface')
    console.log('  - User role modification (admin only)')
    console.log('  - Process permissions editing')
    console.log('  - Account activation/deactivation')
    console.log('  - Password reset functionality')
    console.log('  - Business unit assignment')
    console.log('  - Comprehensive audit logging')
    
    console.log('\n✅ Phase 7B: Business Units & Departments')
    console.log('  - Hierarchical organization structure')
    console.log('  - Manager assignment controls')
    console.log('  - User-to-unit assignment')
    console.log('  - CRUD operations with validation')
    console.log('  - Organizational statistics dashboard')
    
    console.log('\n✅ Phase 7C: Audit Trail Dashboard')
    console.log('  - Comprehensive activity monitoring')
    console.log('  - Advanced filtering and search')
    console.log('  - CSV export functionality (admin only)')
    console.log('  - Security event tracking')
    console.log('  - Compliance reporting ready')

    console.log('\n🚀 Next Steps:')
    console.log('  1. Test user edit functionality via browser')
    console.log('  2. Create sample business units via interface')
    console.log('  3. Perform various admin actions to populate audit trail')
    console.log('  4. Test role-based access controls')
    console.log('  5. Export audit logs for compliance testing')

    console.log('\n🔗 Access URLs:')
    console.log('  - User Management: /settings/users')
    console.log('  - Business Units: /settings/business-units')
    console.log('  - Audit Trail: /settings/audit')
    console.log('  - Settings Dashboard: /settings')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testPhase7Complete()
