const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkBusinessUnitsWithServiceRole() {
  console.log('🔍 Checking Business Units with Service Role')
  
  const { data: units, error } = await supabase
    .from('business_units')
    .select('*')
    .order('name')
    
  if (error) {
    console.error('❌ Error:', error)
  } else {
    console.log(`✅ Found ${units.length} total business units:`)
    units.forEach(unit => {
      console.log(`  - ${unit.name} (Company: ${unit.company_id})`)
    })
  }
  
  const { data: logs } = await supabase
    .from('company_audit_logs')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(3)
    
  console.log(`\n📋 Found ${logs?.length || 0} audit logs:`)
  logs?.forEach(log => {
    console.log(`  - ${log.action_type} by ${log.performed_by}`)
  })
}

checkBusinessUnitsWithServiceRole()
