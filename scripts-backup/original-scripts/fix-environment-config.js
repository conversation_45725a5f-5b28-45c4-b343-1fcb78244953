#!/usr/bin/env node

/**
 * Environment Configuration Fix Script
 * Standardizes Supabase URLs and ports across all applications
 */

const fs = require('fs')
const path = require('path')

// Standard configuration
const STANDARD_CONFIG = {
  SUPABASE_URL: 'http://127.0.0.1:54321',
  SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
  SUPABASE_SERVICE_ROLE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU',
  CUSTOMER_APP_PORT: 3004,
  CONSOLE_APP_PORT: 3008
}

// File paths to update
const CONFIG_FILES = [
  {
    path: 'apps/customer-app/.env.local',
    type: 'customer',
    format: 'env'
  },
  {
    path: 'apps/console-app/.env.local', 
    type: 'console',
    format: 'env'
  },
  {
    path: 'apps/customer-app/package.json',
    type: 'customer',
    format: 'json'
  },
  {
    path: 'apps/console-app/package.json',
    type: 'console', 
    format: 'json'
  },
  {
    path: 'apps/customer-app/vite.config.ts',
    type: 'customer',
    format: 'vite'
  },
  {
    path: 'apps/console-app/vite.config.ts',
    type: 'console',
    format: 'vite'
  }
]

function log(message, type = 'info') {
  const timestamp = new Date().toISOString()
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'
  console.log(`${prefix} [${timestamp}] ${message}`)
}

function createBackup(filePath) {
  if (fs.existsSync(filePath)) {
    const backupPath = `${filePath}.backup.${Date.now()}`
    fs.copyFileSync(filePath, backupPath)
    log(`Created backup: ${backupPath}`)
    return backupPath
  }
  return null
}

function updateEnvFile(filePath, appType) {
  log(`Updating environment file: ${filePath}`)
  
  let content = ''
  
  if (appType === 'customer') {
    content = `# ProcureServe II Customer App Environment Configuration
# Updated: ${new Date().toISOString()}

PUBLIC_SUPABASE_URL=${STANDARD_CONFIG.SUPABASE_URL}
PUBLIC_SUPABASE_ANON_KEY=${STANDARD_CONFIG.SUPABASE_ANON_KEY}
SUPABASE_SERVICE_ROLE_KEY=${STANDARD_CONFIG.SUPABASE_SERVICE_ROLE_KEY}

# App Configuration
PUBLIC_APP_URL=http://localhost:${STANDARD_CONFIG.CUSTOMER_APP_PORT}
PUBLIC_APP_PORT=${STANDARD_CONFIG.CUSTOMER_APP_PORT}

# Development Settings
NODE_ENV=development
DEBUG_AUTH=true
`
  } else if (appType === 'console') {
    content = `# ProcureServe II Console App Environment Configuration  
# Updated: ${new Date().toISOString()}

PUBLIC_SUPABASE_URL=${STANDARD_CONFIG.SUPABASE_URL}
PUBLIC_SUPABASE_ANON_KEY=${STANDARD_CONFIG.SUPABASE_ANON_KEY}
SUPABASE_SERVICE_ROLE_KEY=${STANDARD_CONFIG.SUPABASE_SERVICE_ROLE_KEY}

# App Configuration
PUBLIC_APP_URL=http://localhost:${STANDARD_CONFIG.CONSOLE_APP_PORT}
PUBLIC_APP_PORT=${STANDARD_CONFIG.CONSOLE_APP_PORT}

# Development Settings
NODE_ENV=development
DEBUG_AUTH=true
`
  }
  
  fs.writeFileSync(filePath, content)
  log(`✅ Updated ${filePath}`, 'success')
}

function updatePackageJson(filePath, appType) {
  log(`Updating package.json: ${filePath}`)
  
  if (!fs.existsSync(filePath)) {
    log(`Package.json not found: ${filePath}`, 'error')
    return
  }
  
  const packageJson = JSON.parse(fs.readFileSync(filePath, 'utf8'))
  const port = appType === 'customer' ? STANDARD_CONFIG.CUSTOMER_APP_PORT : STANDARD_CONFIG.CONSOLE_APP_PORT
  
  // Update dev script to use correct port
  if (packageJson.scripts && packageJson.scripts.dev) {
    packageJson.scripts.dev = `vite dev --port ${port}`
    packageJson.scripts.preview = `vite preview --port ${port + 1000}` // Preview on different port
  }
  
  fs.writeFileSync(filePath, JSON.stringify(packageJson, null, 2))
  log(`✅ Updated ${filePath}`, 'success')
}

function updateViteConfig(filePath, appType) {
  log(`Updating Vite config: ${filePath}`)
  
  if (!fs.existsSync(filePath)) {
    log(`Vite config not found: ${filePath}`, 'error')
    return
  }
  
  const port = appType === 'customer' ? STANDARD_CONFIG.CUSTOMER_APP_PORT : STANDARD_CONFIG.CONSOLE_APP_PORT
  
  let content = fs.readFileSync(filePath, 'utf8')
  
  // Update server port configuration
  const serverConfigRegex = /server:\s*{[^}]*}/s
  const newServerConfig = `server: {
    port: ${port},
    host: true,
    strictPort: true
  }`
  
  if (serverConfigRegex.test(content)) {
    content = content.replace(serverConfigRegex, newServerConfig)
  } else {
    // Add server config if it doesn't exist
    content = content.replace(
      /export default defineConfig\(\{/,
      `export default defineConfig({
  ${newServerConfig},`
    )
  }
  
  fs.writeFileSync(filePath, content)
  log(`✅ Updated ${filePath}`, 'success')
}

function validateConfiguration() {
  log('Validating configuration...')
  
  const issues = []
  
  // Check if all required files exist
  CONFIG_FILES.forEach(file => {
    if (!fs.existsSync(file.path)) {
      issues.push(`Missing file: ${file.path}`)
    }
  })
  
  // Check environment files have correct URLs
  const customerEnv = 'apps/customer-app/.env.local'
  const consoleEnv = 'apps/console-app/.env.local'
  
  if (fs.existsSync(customerEnv)) {
    const content = fs.readFileSync(customerEnv, 'utf8')
    if (!content.includes(STANDARD_CONFIG.SUPABASE_URL)) {
      issues.push(`Customer app has incorrect Supabase URL`)
    }
  }
  
  if (fs.existsSync(consoleEnv)) {
    const content = fs.readFileSync(consoleEnv, 'utf8')
    if (!content.includes(STANDARD_CONFIG.SUPABASE_URL)) {
      issues.push(`Console app has incorrect Supabase URL`)
    }
  }
  
  if (issues.length > 0) {
    log('Configuration issues found:', 'error')
    issues.forEach(issue => log(`  - ${issue}`, 'error'))
    return false
  }
  
  log('✅ Configuration validation passed', 'success')
  return true
}

function main() {
  log('🔧 Starting environment configuration fix...')
  
  try {
    // Create backups
    log('Creating backups...')
    CONFIG_FILES.forEach(file => {
      if (fs.existsSync(file.path)) {
        createBackup(file.path)
      }
    })
    
    // Update configuration files
    log('Updating configuration files...')
    
    CONFIG_FILES.forEach(file => {
      switch (file.format) {
        case 'env':
          updateEnvFile(file.path, file.type)
          break
        case 'json':
          updatePackageJson(file.path, file.type)
          break
        case 'vite':
          updateViteConfig(file.path, file.type)
          break
      }
    })
    
    // Validate configuration
    if (validateConfiguration()) {
      log('🎉 Environment configuration fix completed successfully!', 'success')
      log('')
      log('Next steps:')
      log('1. Restart development servers')
      log('2. Test authentication flows')
      log('3. Verify both apps connect to same Supabase instance')
      log('')
      log('Customer App: http://localhost:3004')
      log('Console App: http://localhost:3008')
      log('Supabase: http://127.0.0.1:54321')
    } else {
      log('❌ Configuration validation failed', 'error')
      process.exit(1)
    }
    
  } catch (error) {
    log(`Error during configuration fix: ${error.message}`, 'error')
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = {
  STANDARD_CONFIG,
  updateEnvFile,
  updatePackageJson,
  updateViteConfig,
  validateConfiguration
}
