// Test script to verify registration flow
const fetch = require('node-fetch');

async function testRegistration() {
  try {
    console.log('Testing candidate registration...');
    
    const testData = {
      first_name: 'Test',
      last_name: 'User',
      email: `test${Date.now()}@example.com`,
      password: 'password123',
      confirm_password: 'password123',
      phone: '555-1234'
    };
    
    console.log('Submitting registration with data:', testData);
    
    const response = await fetch('http://localhost:3006/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams(testData).toString(),
      redirect: 'manual' // Don't follow redirects automatically
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.status === 303 || response.status === 302) {
      console.log('Registration successful - redirected to:', response.headers.get('location'));
    } else {
      const responseText = await response.text();
      console.log('Response body:', responseText);
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testRegistration();
