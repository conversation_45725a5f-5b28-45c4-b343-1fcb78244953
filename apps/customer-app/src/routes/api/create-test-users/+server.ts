// Create test users via Supabase Admin API
import type { RequestHandler } from './$types'
import { adminSupabase } from '$lib/supabase-admin'

export const POST: RequestHandler = async () => {
  try {
    // First, check if users already exist
    const { data: existingUsers } = await adminSupabase.auth.admin.listUsers()
    const existingEmails = existingUsers.users?.map(u => u.email) || []

    const usersToCreate = [
      { email: '<EMAIL>', password: 'password123', role: 'admin' },
      { email: '<EMAIL>', password: 'password123', role: 'manager' }
    ]

    const results = []

    for (const userInfo of usersToCreate) {
      if (existingEmails.includes(userInfo.email)) {
        results.push({
          email: userInfo.email,
          success: true,
          message: 'User already exists',
          action: 'skipped'
        })
        continue
      }

      // Create user in Supabase Auth
      const { data: authUser, error: authError } = await adminSupabase.auth.admin.createUser({
        email: userInfo.email,
        password: userInfo.password,
        email_confirm: true,
        user_metadata: {
          role: userInfo.role
        }
      })

      if (authError) {
        results.push({
          email: userInfo.email,
          success: false,
          error: authError.message,
          action: 'failed'
        })
        continue
      }

      // Update the existing user record in our users table with the auth user ID
      const { error: userUpdateError } = await adminSupabase
        .from('users')
        .update({ id: authUser.user.id })
        .eq('email', userInfo.email)

      if (userUpdateError) {
        console.error('Failed to link user record:', userUpdateError)
      }

      results.push({
        email: userInfo.email,
        success: true,
        message: 'User created successfully',
        id: authUser.user.id,
        action: 'created'
      })
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    return new Response(JSON.stringify({
      message: `User creation complete: ${successCount} successful, ${failCount} failed`,
      results,
      summary: {
        total: results.length,
        successful: successCount,
        failed: failCount
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (err) {
    console.error('User creation error:', err)
    return new Response(JSON.stringify({
      error: 'Failed to create users',
      details: err instanceof Error ? err.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
