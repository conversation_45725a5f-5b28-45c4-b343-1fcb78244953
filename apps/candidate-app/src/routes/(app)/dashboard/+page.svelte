<script lang="ts">
  import { Briefcase, FileText, Eye, TrendingUp } from 'lucide-svelte'
  import type { PageData } from './$types'
  
  let { data }: { data: PageData } = $props()

  let { candidateProfile } = $derived(data)
  
  // Mock data for dashboard stats
  const stats = [
    { label: 'Applications Sent', value: '12', icon: FileText, color: 'blue' },
    { label: 'Profile Views', value: '48', icon: Eye, color: 'green' },
    { label: 'Jobs Matched', value: '6', icon: Briefcase, color: 'purple' },
    { label: 'Response Rate', value: '25%', icon: TrendingUp, color: 'orange' }
  ]
  
  const recentApplications = [
    { job: 'Senior Software Engineer', company: 'TechCorp', status: 'Under Review', date: '2 days ago' },
    { job: 'Full Stack Developer', company: 'StartupXYZ', status: 'Interview Scheduled', date: '1 week ago' },
    { job: 'Frontend Developer', company: 'BigTech Inc', status: 'Applied', date: '1 week ago' }
  ]
  
  const recommendedJobs = [
    { title: 'React Developer', company: 'InnovateLabs', location: 'Remote', salary: '$80k - $100k' },
    { title: 'Software Engineer', company: 'TechFlow', location: 'San Francisco, CA', salary: '$90k - $120k' },
    { title: 'Full Stack Developer', company: 'CloudServe', location: 'Austin, TX', salary: '$85k - $110k' }
  ]
</script>

<svelte:head>
  <title>Dashboard - Candidate Portal</title>
</svelte:head>

<div class="space-y-6">
  <!-- Welcome Section -->
  <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 text-white">
    <h1 class="text-2xl font-bold mb-2">
      Welcome back, {candidateProfile?.first_name || 'there'}!
    </h1>
    <p class="text-blue-100">
      Ready to find your next opportunity? Let's get started.
    </p>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    {#each stats as stat}
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-{stat.color}-100 rounded-lg flex items-center justify-center">
              {@const IconComponent = stat.icon}
              <IconComponent class="w-5 h-5 text-{stat.color}-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{stat.label}</p>
            <p class="text-2xl font-bold text-gray-900">{stat.value}</p>
          </div>
        </div>
      </div>
    {/each}
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Applications -->
    <div class="bg-white rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Recent Applications</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          {#each recentApplications as app}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <h3 class="font-medium text-gray-900">{app.job}</h3>
                <p class="text-sm text-gray-600">{app.company}</p>
              </div>
              <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                  {app.status === 'Interview Scheduled' ? 'bg-green-100 text-green-800' :
                    app.status === 'Under Review' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'}">
                  {app.status}
                </span>
                <p class="text-xs text-gray-500 mt-1">{app.date}</p>
              </div>
            </div>
          {/each}
        </div>
        <div class="mt-4">
          <a href="/applications" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
            View all applications →
          </a>
        </div>
      </div>
    </div>

    <!-- Recommended Jobs -->
    <div class="bg-white rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Recommended for You</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          {#each recommendedJobs as job}
            <div class="p-3 border border-gray-200 rounded-lg hover:border-blue-300 cursor-pointer transition-colors">
              <h3 class="font-medium text-gray-900">{job.title}</h3>
              <p class="text-sm text-gray-600">{job.company}</p>
              <div class="flex items-center justify-between mt-2">
                <span class="text-xs text-gray-500">{job.location}</span>
                <span class="text-xs font-medium text-green-600">{job.salary}</span>
              </div>
            </div>
          {/each}
        </div>
        <div class="mt-4">
          <a href="/jobs" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
            Browse all jobs →
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white rounded-lg border border-gray-200 p-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <a href="/profile" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Briefcase class="w-5 h-5 text-blue-600" />
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900">Update Profile</p>
          <p class="text-xs text-gray-500">Keep your information current</p>
        </div>
      </a>
      
      <a href="/jobs" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <FileText class="w-5 h-5 text-green-600" />
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900">Search Jobs</p>
          <p class="text-xs text-gray-500">Find new opportunities</p>
        </div>
      </a>
      
      <a href="/settings" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <TrendingUp class="w-5 h-5 text-purple-600" />
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900">Job Preferences</p>
          <p class="text-xs text-gray-500">Customize your search</p>
        </div>
      </a>
    </div>
  </div>
</div>
