import { createSupabaseServerClient } from '$lib/supabase'
import { redirect } from '@sveltejs/kit'

export const load = async ({ cookies }) => {
  const supabase = createSupabaseServerClient(cookies)

  const { data: { session } } = await supabase.auth.getSession()
  
  // Redirect unauthenticated users to login
  if (!session) {
    throw redirect(302, '/login')
  }

  // Get candidate profile if user is authenticated
  let candidateProfile = null
  if (session.user) {
    const { data } = await supabase
      .from('candidates')
      .select('*')
      .eq('auth_user_id', session.user.id)
      .single()
    
    candidateProfile = data
  }

  return {
    session,
    user: session.user,
    candidateProfile
  }
}
