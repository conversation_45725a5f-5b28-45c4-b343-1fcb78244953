<script lang="ts">
  import { enhance } from '$app/forms'
  import { <PERSON>r<PERSON>heck, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-svelte'
  import type { ActionData } from './$types'

  let { form }: { form: ActionData } = $props()

  let loading = $state(false)
  let email = $state('')
  let submitted = $state(false)

  function handleSubmit() {
    submitted = true
  }
</script>

<svelte:head>
  <title>Reset Password - Talent Portal</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background px-4">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
        <UserCheck class="w-6 h-6 text-white" />
      </div>
      <h2 class="text-3xl font-bold text-gray-900">Reset Password</h2>
      <p class="mt-2 text-sm text-gray-600">
        Enter your email to receive reset instructions
      </p>
    </div>

    {#if submitted && !form?.error}
      <!-- Success State -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="text-center">
          <div class="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle class="w-6 h-6 text-green-600" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Check Your Email</h3>
          <p class="text-gray-600 mb-6">
            We've sent password reset instructions to <strong>{email}</strong>
          </p>
          <div class="space-y-3">
            <p class="text-sm text-gray-500">
              Didn't receive the email? Check your spam folder or try again.
            </p>
            <button
              onclick={() => { submitted = false; email = '' }}
              class="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Try different email
            </button>
          </div>
        </div>
      </div>
    {:else}
      <!-- Error Message -->
      {#if form?.error}
        <div class="rounded-md bg-red-50 p-4 border border-red-200">
          <div class="flex">
            <AlertCircle class="h-5 w-5 text-red-400" />
            <div class="ml-3">
              <div class="text-sm text-red-800">
                {form.error}
              </div>
            </div>
          </div>
        </div>
      {/if}

      <!-- Reset Form -->
      <form 
        method="POST" 
        action="?/reset"
        use:enhance={() => {
          loading = true
          return async ({ update }) => {
            loading = false
            await update()
            handleSubmit()
          }
        }}
        class="bg-white rounded-lg border border-gray-200 p-6 space-y-6"
      >
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
            Email Address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autocomplete="email"
            required
            bind:value={email}
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your email address"
            disabled={loading}
          />
        </div>

        <button
          type="submit"
          disabled={loading || !email}
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {#if loading}
            <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            Sending...
          {:else}
            Send Reset Instructions
          {/if}
        </button>
      </form>
    {/if}

    <!-- Footer Links -->
    <div class="text-center space-y-4">
      <a href="/login" class="inline-flex items-center text-sm text-gray-600 hover:text-blue-600 transition-colors">
        <ArrowLeft class="w-4 h-4 mr-1" />
        Back to Sign In
      </a>

      <p class="text-xs text-gray-500">
        Looking for business portal?
        <a href="https://app.procureserve.com" class="text-blue-600 hover:text-blue-500">
          Click here
        </a>
      </p>
    </div>
  </div>
</div>
