import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseServerClient } from '$lib/supabase'

export const actions = {
  login: async ({ request, cookies }) => {
    const supabase = createSupabaseServerClient(cookies)

    const formData = await request.formData()
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    if (!email || !password) {
      return fail(400, {
        error: 'Email and password are required',
        email
      })
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      console.error('Auth error:', error)
      return fail(400, {
        error: 'Invalid email or password',
        email
      })
    }

    // Check if user has a candidate profile
    const { data: candidateData } = await supabase
      .from('candidates')
      .select('id')
      .eq('user_id', data.user.id)
      .single()

    if (!candidateData) {
      // Redirect to profile creation if no candidate profile exists
      throw redirect(302, '/complete-profile')
    }

    throw redirect(302, '/dashboard')
  }
}
