import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseServerClient } from '$lib/supabase'
import type { Actions } from './$types'

export const actions: Actions = {
  register: async ({ request, cookies }) => {
    const formData = await request.formData()
    const email = formData.get('email') as string
    const password = formData.get('password') as string
    const confirmPassword = formData.get('confirm_password') as string
    const firstName = formData.get('first_name') as string
    const lastName = formData.get('last_name') as string
    const phone = formData.get('phone') as string

    // Validation
    if (!email || !password || !firstName || !lastName) {
      return fail(400, {
        error: 'Please fill in all required fields'
      })
    }

    if (password !== confirmPassword) {
      return fail(400, {
        error: 'Passwords do not match'
      })
    }

    if (password.length < 6) {
      return fail(400, {
        error: 'Password must be at least 6 characters long'
      })
    }

    const supabase = createSupabaseServerClient(cookies)

    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            user_type: 'candidate'
          }
        }
      })

      if (authError) {
        console.error('Auth error:', authError)
        return fail(400, {
          error: authError.message
        })
      }

      if (!authData.user) {
        return fail(400, {
          error: 'Failed to create user account'
        })
      }

      // Create candidate profile
      const fullName = `${firstName} ${lastName}`.trim()
      const { error: profileError } = await supabase
        .from('candidates')
        .insert({
          auth_user_id: authData.user.id,
          first_name: firstName,
          last_name: lastName,
          name: fullName, // Required field from original schema
          email,
          phone: phone || null,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (profileError) {
        console.error('Profile creation error:', profileError)
        // Clean up auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id)
        return fail(400, {
          error: 'Failed to create candidate profile'
        })
      }

      // Redirect to dashboard
      throw redirect(303, '/dashboard')

    } catch (error) {
      console.error('Registration error:', error)
      return fail(500, {
        error: 'An unexpected error occurred. Please try again.'
      })
    }
  }
}
