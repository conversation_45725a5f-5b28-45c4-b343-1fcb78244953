<script lang="ts">
  import { enhance } from '$app/forms'
  import { <PERSON>r<PERSON>he<PERSON>, Eye, EyeOff, AlertCircle } from 'lucide-svelte'
  import Button from '$lib/components/ui/button.svelte'
  import Input from '$lib/components/ui/input.svelte'
  import Label from '$lib/components/ui/label.svelte'
  import Card from '$lib/components/ui/card.svelte'
  import CardHeader from '$lib/components/ui/card-header.svelte'
  import CardContent from '$lib/components/ui/card-content.svelte'
  import CardTitle from '$lib/components/ui/card-title.svelte'
  import type { ActionData } from './$types'

  let { form }: { form: ActionData } = $props()

  let loading = $state(false)
  let showPassword = $state(false)
  let showConfirmPassword = $state(false)

  let formData = $state({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    password: '',
    confirm_password: ''
  })

  // Enhanced validation states
  let fieldErrors = $state<Record<string, string>>({})
  let fieldTouched = $state<Record<string, boolean>>({})

  // Real-time validation functions
  function validateEmailRealtime(email: string): string {
    if (!email) return ''
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) return 'Please enter a valid email address'
    return ''
  }

  function validatePasswordRealtime(password: string): string {
    if (!password) return ''
    if (password.length < 8) return 'Password must be at least 8 characters long'
    if (!/[A-Z]/.test(password)) return 'Password must contain an uppercase letter'
    if (!/[a-z]/.test(password)) return 'Password must contain a lowercase letter'
    if (!/\d/.test(password)) return 'Password must contain a number'
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) return 'Password must contain a special character'
    return ''
  }

  function validateNameRealtime(name: string, fieldName: string): string {
    if (!name) return ''
    if (name.trim().length < 2) return `${fieldName} must be at least 2 characters long`
    if (name.length > 50) return `${fieldName} must be less than 50 characters`
    if (!/^[a-zA-Z\s'-]+$/.test(name)) return `${fieldName} can only contain letters, spaces, hyphens, and apostrophes`
    return ''
  }

  function validatePhoneRealtime(phone: string): string {
    if (!phone) return '' // Phone is optional
    const digitsOnly = phone.replace(/\D/g, '')
    if (digitsOnly.length < 10 || digitsOnly.length > 15) return 'Phone number must be between 10 and 15 digits'
    return ''
  }

  // Reactive validation
  $effect(() => {
    if (fieldTouched.email) {
      fieldErrors.email = validateEmailRealtime(formData.email)
    }
  })

  $effect(() => {
    if (fieldTouched.first_name) {
      fieldErrors.first_name = validateNameRealtime(formData.first_name, 'First name')
    }
  })

  $effect(() => {
    if (fieldTouched.last_name) {
      fieldErrors.last_name = validateNameRealtime(formData.last_name, 'Last name')
    }
  })

  $effect(() => {
    if (fieldTouched.phone) {
      fieldErrors.phone = validatePhoneRealtime(formData.phone)
    }
  })

  $effect(() => {
    if (fieldTouched.password) {
      fieldErrors.password = validatePasswordRealtime(formData.password)
    }
  })

  $effect(() => {
    if (fieldTouched.confirm_password) {
      if (!formData.confirm_password) {
        fieldErrors.confirm_password = ''
      } else if (formData.password !== formData.confirm_password) {
        fieldErrors.confirm_password = 'Passwords do not match'
      } else {
        fieldErrors.confirm_password = ''
      }
    }
  })

  let passwordsMatch = $derived(formData.password === formData.confirm_password)
  let hasValidationErrors = $derived(Object.values(fieldErrors).some(error => error !== ''))
  let formValid = $derived(
    formData.first_name &&
    formData.last_name &&
    formData.email &&
    formData.password &&
    formData.confirm_password &&
    passwordsMatch &&
    !hasValidationErrors
  )

  function markFieldTouched(fieldName: string) {
    fieldTouched[fieldName] = true
  }

  function getPasswordStrength(password: string): number {
    if (!password) return 0

    let score = 0

    // Length check
    if (password.length >= 8) score++

    // Character variety checks
    if (/[A-Z]/.test(password)) score++
    if (/[a-z]/.test(password)) score++
    if (/\d/.test(password)) score++
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score++

    // Bonus for longer passwords
    if (password.length >= 12) score++

    return Math.min(score, 4)
  }

  function togglePasswordVisibility() {
    showPassword = !showPassword
  }

  function toggleConfirmPasswordVisibility() {
    showConfirmPassword = !showConfirmPassword
  }
</script>

<svelte:head>
  <title>Register - Candidate Portal</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-4">
        <UserCheck class="w-6 h-6 text-primary-foreground" />
      </div>
      <h2 class="text-3xl font-bold text-foreground">Create Your Profile</h2>
      <p class="mt-2 text-sm text-muted-foreground">
        Join our talent network and discover opportunities
      </p>
    </div>

    <!-- Error Message -->
    {#if form?.error}
      <div class="rounded-md bg-red-50 border border-red-200 p-4">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-red-400 flex-shrink-0" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Registration Error</h3>
            <div class="mt-2 text-sm text-red-700">
              {form.error}
            </div>
            {#if form && 'allPasswordErrors' in form && Array.isArray(form.allPasswordErrors) && form.allPasswordErrors.length > 1}
              <div class="mt-2">
                <p class="text-xs text-red-600 font-medium">Password requirements:</p>
                <ul class="mt-1 text-xs text-red-600 list-disc list-inside">
                  {#each form.allPasswordErrors as error}
                    <li>{error}</li>
                  {/each}
                </ul>
              </div>
            {/if}
          </div>
        </div>
      </div>
    {/if}

    <!-- Success Message for Email Confirmation -->
    {#if form && 'success' in form && form.success}
      <div class="rounded-md bg-green-50 border border-green-200 p-4">
        <div class="flex">
          <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Registration Successful</h3>
            <div class="mt-2 text-sm text-green-700">
              Please check your email to verify your account before signing in.
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- Registration Form -->
    <Card class="mt-8">
      <CardHeader>
        <CardTitle>Create Your Account</CardTitle>
      </CardHeader>
      <CardContent>
        <form
          method="POST"
          action="?/register"
          use:enhance={() => {
            loading = true
            return async ({ update }) => {
              loading = false
              await update()
            }
          }}
          class="space-y-6"
        >
          <div class="space-y-4">
            <!-- Name Fields -->
            <div class="grid grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label for="first_name">First Name</Label>
                <Input
                  id="first_name"
                  name="first_name"
                  type="text"
                  required
                  bind:value={formData.first_name}
                  placeholder="John"
                  disabled={loading}
                  class={fieldErrors.first_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                  onblur={() => markFieldTouched('first_name')}
                />
                {#if fieldErrors.first_name}
                  <p class="text-sm text-red-600">{fieldErrors.first_name}</p>
                {/if}
              </div>
              <div class="space-y-2">
                <Label for="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  name="last_name"
                  type="text"
                  required
                  bind:value={formData.last_name}
                  placeholder="Doe"
                  disabled={loading}
                  class={fieldErrors.last_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                  onblur={() => markFieldTouched('last_name')}
                />
                {#if fieldErrors.last_name}
                  <p class="text-sm text-red-600">{fieldErrors.last_name}</p>
                {/if}
              </div>
            </div>

            <!-- Email -->
            <div class="space-y-2">
              <Label for="email">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                autocomplete="email"
                required
                bind:value={formData.email}
                placeholder="<EMAIL>"
                disabled={loading}
                class={fieldErrors.email ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                onblur={() => markFieldTouched('email')}
              />
              {#if fieldErrors.email}
                <p class="text-sm text-red-600">{fieldErrors.email}</p>
              {/if}
            </div>

            <!-- Phone -->
            <div class="space-y-2">
              <Label for="phone">Phone Number (Optional)</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                bind:value={formData.phone}
                placeholder="(*************"
                disabled={loading}
                class={fieldErrors.phone ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                onblur={() => markFieldTouched('phone')}
              />
              {#if fieldErrors.phone}
                <p class="text-sm text-red-600">{fieldErrors.phone}</p>
              {/if}
            </div>

            <!-- Password -->
            <div class="space-y-2">
              <Label for="password">Password</Label>
              <div class="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autocomplete="new-password"
                  required
                  bind:value={formData.password}
                  placeholder="Create a strong password"
                  disabled={loading}
                  class={`pr-10 ${fieldErrors.password ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                  onblur={() => markFieldTouched('password')}
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onclick={togglePasswordVisibility}
                  disabled={loading}
                >
                  {#if showPassword}
                    <EyeOff class="h-4 w-4 text-muted-foreground" />
                  {:else}
                    <Eye class="h-4 w-4 text-muted-foreground" />
                  {/if}
                </button>
              </div>
              {#if fieldErrors.password}
                <p class="text-sm text-red-600">{fieldErrors.password}</p>
              {/if}
              {#if formData.password && !fieldErrors.password}
                <div class="text-xs text-green-600 flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  Password meets all requirements
                </div>
              {/if}
            </div>

            <!-- Confirm Password -->
            <div class="space-y-2">
              <Label for="confirm_password">Confirm Password</Label>
              <div class="relative">
                <Input
                  id="confirm_password"
                  name="confirm_password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  autocomplete="new-password"
                  required
                  bind:value={formData.confirm_password}
                  placeholder="Confirm your password"
                  disabled={loading}
                  class={`pr-10 ${fieldErrors.confirm_password ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                  onblur={() => markFieldTouched('confirm_password')}
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onclick={toggleConfirmPasswordVisibility}
                  disabled={loading}
                >
                  {#if showConfirmPassword}
                    <EyeOff class="h-4 w-4 text-muted-foreground" />
                  {:else}
                    <Eye class="h-4 w-4 text-muted-foreground" />
                  {/if}
                </button>
              </div>
              {#if fieldErrors.confirm_password}
                <p class="text-sm text-red-600">{fieldErrors.confirm_password}</p>
              {:else if passwordsMatch && formData.confirm_password}
                <div class="text-xs text-green-600 flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  Passwords match
                </div>
              {/if}
            </div>
          </div>

          <!-- Password Strength Indicator -->
          {#if formData.password}
            <div class="space-y-2">
              <div class="text-xs text-muted-foreground">Password Strength</div>
              <div class="flex space-x-1">
                {#each Array(4) as _, i}
                  <div class={`h-1 flex-1 rounded ${
                    i < getPasswordStrength(formData.password)
                      ? getPasswordStrength(formData.password) === 1 ? 'bg-red-500'
                        : getPasswordStrength(formData.password) === 2 ? 'bg-yellow-500'
                        : getPasswordStrength(formData.password) === 3 ? 'bg-blue-500'
                        : 'bg-green-500'
                      : 'bg-gray-200'
                  }`}></div>
                {/each}
              </div>
              <div class="text-xs text-muted-foreground">
                {getPasswordStrength(formData.password) === 1 ? 'Weak'
                  : getPasswordStrength(formData.password) === 2 ? 'Fair'
                  : getPasswordStrength(formData.password) === 3 ? 'Good'
                  : getPasswordStrength(formData.password) === 4 ? 'Strong'
                  : 'Very Weak'}
              </div>
            </div>
          {/if}

          <!-- Submit Button -->
          <Button
            type="submit"
            disabled={loading || !formValid}
            class={`w-full ${!formValid && Object.keys(fieldTouched).length > 0 ? 'opacity-50' : ''}`}
          >
            {#if loading}
              <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
              Creating Account...
            {:else}
              Create Account
            {/if}
          </Button>

          {#if !formValid && Object.keys(fieldTouched).length > 0}
            <p class="text-xs text-muted-foreground text-center">
              Please fix the errors above to continue
            </p>
          {/if}
        </form>
      </CardContent>
    </Card>

    <!-- Footer Links -->
    <div class="text-center space-y-4">
      <p class="text-sm text-muted-foreground">
        Already have an account?
        <a href="/login" class="font-medium text-primary hover:text-primary/80">
          Sign in here
        </a>
      </p>

      <p class="text-xs text-muted-foreground">
        Looking for business portal?
        <a href="https://app.procureserve.com" class="text-primary hover:text-primary/80">
          Click here
        </a>
      </p>
    </div>
  </div>
</div>
