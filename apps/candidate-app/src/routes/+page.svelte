<script lang="ts">
	import { goto } from '$app/navigation'
	import { onMount } from 'svelte'
	import type { PageData } from './$types'

	let { data }: { data: PageData } = $props()

	// Redirect based on authentication status
	onMount(() => {
		if (data.session?.user) {
			goto('/dashboard')
		} else {
			// Redirect directly to login page for better UX
			goto('/login')
		}
	})
</script>

<svelte:head>
	<title>Talent Portal - ProcureServe</title>
</svelte:head>

<!-- Loading state while redirecting -->
<div class="min-h-screen bg-background flex items-center justify-center">
	<div class="text-center">
		<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
		<p class="text-muted-foreground">Loading...</p>
	</div>
</div>
