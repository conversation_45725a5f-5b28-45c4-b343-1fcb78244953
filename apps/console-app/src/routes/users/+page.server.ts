import { ConsoleAuthManager } from '$lib/server/auth/console-auth'
import { redirect, error } from '@sveltejs/kit'

export const load = async ({ locals }) => {
  // Check if user is authenticated
  if (!locals.user || !locals.consoleUser) {
    throw redirect(302, '/login')
  }

  const authManager = new ConsoleAuthManager()
  
  // Check if user has permission to view users
  const hasPermission = await authManager.validatePermission(
    locals.consoleUser.id,
    'users',
    'read'
  )

  if (!hasPermission) {
    throw error(403, 'Insufficient permissions to access user management')
  }

  // Get users based on permissions
  let users = []
  
  if (locals.consoleUser.role === 'super_admin') {
    // Super admins can see all console users
    const { data } = await locals.supabase
      .from('console_users')
      .select('*')
      .order('email')
    
    users = data || []
  } else {
    // Company admins see users from their companies only
    const companyIds = locals.consoleUser.company_ids || []
    if (companyIds.length > 0) {
      const { data } = await locals.supabase
        .from('console_users')
        .select('*')
        .filter('company_ids', 'cs', `{${companyIds.join(',')}}`)
        .order('email')
      
      users = data || []
    }
  }

  return {
    users,
    userRole: locals.consoleUser.role,
    permissions: locals.consoleUser.permissions
  }
}
