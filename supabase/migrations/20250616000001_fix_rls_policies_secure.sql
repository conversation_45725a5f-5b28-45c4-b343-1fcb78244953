-- CRITICAL SECURITY FIX: Restore Row-Level Security with Non-Recursive Policies
-- This migration fixes the RLS recursion issues while maintaining full security
-- Date: 2025-06-16
-- Priority: CRITICAL

-- First, ensure all tables have RLS enabled
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE candidates ENABLE ROW LEVEL SECURITY;
ALTER TABLE applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultants ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE placements ENABLE ROW LEVEL SECURITY;
ALTER TABLE configurable_enums ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

-- Drop all existing problematic policies to start fresh
DROP POLICY IF EXISTS "Company isolation" ON companies;
DROP POLICY IF EXISTS "Users can access their company data" ON users;
DROP POLICY IF EXISTS "Users can access own company data" ON users;
DROP POLICY IF EXISTS "Allow user creation during registration" ON users;
DROP POLICY IF EXISTS "Jobs are company-scoped" ON jobs;
DROP POLICY IF EXISTS "Candidates can manage own data" ON candidates;
DROP POLICY IF EXISTS "Allow candidate creation during registration" ON candidates;
DROP POLICY IF EXISTS "Companies can see candidates with applications" ON candidates;
DROP POLICY IF EXISTS "Applications are company-scoped" ON applications;
DROP POLICY IF EXISTS "Allow application creation" ON applications;

-- Create a secure function to get user's company_id without recursion
CREATE OR REPLACE FUNCTION auth.get_user_company_id()
RETURNS UUID
LANGUAGE SQL
SECURITY DEFINER
STABLE
AS $$
  SELECT company_id 
  FROM public.users 
  WHERE id = auth.uid()
  LIMIT 1;
$$;

-- Create a function to check if user is admin without recursion
CREATE OR REPLACE FUNCTION auth.is_admin_user()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
STABLE
AS $$
  SELECT role = 'admin'
  FROM public.users 
  WHERE id = auth.uid()
  LIMIT 1;
$$;

-- SECURE COMPANIES POLICIES
-- Companies can be viewed by their own users or admins
CREATE POLICY "companies_select_policy" ON companies
  FOR SELECT USING (
    id = auth.get_user_company_id() OR
    auth.is_admin_user()
  );

-- Only admins can modify companies
CREATE POLICY "companies_modify_policy" ON companies
  FOR ALL USING (
    auth.is_admin_user()
  );

-- SECURE USERS POLICIES  
-- Users can view themselves and other users in their company
CREATE POLICY "users_select_policy" ON users
  FOR SELECT USING (
    id = auth.uid() OR
    company_id = auth.get_user_company_id()
  );

-- Users can update their own profile
CREATE POLICY "users_update_self_policy" ON users
  FOR UPDATE USING (
    id = auth.uid()
  );

-- Admins can update users in their company
CREATE POLICY "users_update_admin_policy" ON users
  FOR UPDATE USING (
    auth.is_admin_user() AND
    company_id = auth.get_user_company_id()
  );

-- Allow user creation during registration (no auth.uid() yet)
CREATE POLICY "users_insert_registration_policy" ON users
  FOR INSERT WITH CHECK (true);

-- SECURE JOBS POLICIES
-- Jobs are scoped to user's company
CREATE POLICY "jobs_company_policy" ON jobs
  FOR ALL USING (
    company_id = auth.get_user_company_id()
  );

-- SECURE CANDIDATES POLICIES
-- Candidates can manage their own data
CREATE POLICY "candidates_self_policy" ON candidates
  FOR ALL USING (
    auth_user_id = auth.uid()
  );

-- Companies can see candidates who have applied to their jobs
CREATE POLICY "candidates_company_policy" ON candidates
  FOR SELECT USING (
    id IN (
      SELECT candidate_id 
      FROM applications 
      WHERE company_id = auth.get_user_company_id()
    )
  );

-- Allow candidate creation during registration
CREATE POLICY "candidates_insert_policy" ON candidates
  FOR INSERT WITH CHECK (true);

-- SECURE APPLICATIONS POLICIES
-- Applications are scoped to companies and candidates
CREATE POLICY "applications_company_policy" ON applications
  FOR ALL USING (
    company_id = auth.get_user_company_id() OR
    candidate_id IN (
      SELECT id FROM candidates WHERE auth_user_id = auth.uid()
    )
  );

-- Allow application creation
CREATE POLICY "applications_insert_policy" ON applications
  FOR INSERT WITH CHECK (true);

-- SECURE CONSULTANTS POLICIES (Bench Sales)
-- Consultants are scoped to user's company
CREATE POLICY "consultants_company_policy" ON consultants
  FOR ALL USING (
    company_id = auth.get_user_company_id()
  );

-- SECURE PROJECTS POLICIES (Bench Sales)
-- Projects are scoped to user's company
CREATE POLICY "projects_company_policy" ON projects
  FOR ALL USING (
    company_id = auth.get_user_company_id()
  );

-- SECURE CLIENTS POLICIES (Bench Sales)
-- Clients are scoped to user's company
CREATE POLICY "clients_company_policy" ON clients
  FOR ALL USING (
    company_id = auth.get_user_company_id()
  );

-- SECURE PLACEMENTS POLICIES (Bench Sales)
-- Placements are scoped to user's company
CREATE POLICY "placements_company_policy" ON placements
  FOR ALL USING (
    company_id = auth.get_user_company_id()
  );

-- SECURE CONFIGURABLE ENUMS POLICIES
-- Enums are scoped to user's company
CREATE POLICY "enums_company_policy" ON configurable_enums
  FOR ALL USING (
    company_id = auth.get_user_company_id()
  );

-- SECURE ACTIVITY LOGS POLICIES
-- Activity logs are scoped to user's company
CREATE POLICY "activity_logs_company_policy" ON activity_logs
  FOR ALL USING (
    company_id = auth.get_user_company_id()
  );

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_users_company_id ON users(company_id);
CREATE INDEX IF NOT EXISTS idx_users_auth_uid ON users(id) WHERE id = auth.uid();
CREATE INDEX IF NOT EXISTS idx_jobs_company_id ON jobs(company_id);
CREATE INDEX IF NOT EXISTS idx_candidates_auth_user_id ON candidates(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_applications_company_id ON applications(company_id);
CREATE INDEX IF NOT EXISTS idx_applications_candidate_id ON applications(candidate_id);
CREATE INDEX IF NOT EXISTS idx_consultants_company_id ON consultants(company_id);
CREATE INDEX IF NOT EXISTS idx_projects_company_id ON projects(company_id);
CREATE INDEX IF NOT EXISTS idx_clients_company_id ON clients(company_id);
CREATE INDEX IF NOT EXISTS idx_placements_company_id ON placements(company_id);

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT EXECUTE ON FUNCTION auth.get_user_company_id() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.is_admin_user() TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION auth.get_user_company_id() IS 'Securely retrieves the company_id for the current authenticated user without RLS recursion';
COMMENT ON FUNCTION auth.is_admin_user() IS 'Checks if the current authenticated user has admin role without RLS recursion';

-- Verify RLS is enabled on all tables
DO $$
DECLARE
    table_name TEXT;
    rls_enabled BOOLEAN;
BEGIN
    FOR table_name IN 
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename IN ('companies', 'users', 'jobs', 'candidates', 'applications', 'consultants', 'projects', 'clients', 'placements', 'configurable_enums', 'activity_logs')
    LOOP
        SELECT relrowsecurity INTO rls_enabled
        FROM pg_class 
        WHERE relname = table_name;
        
        IF NOT rls_enabled THEN
            RAISE EXCEPTION 'RLS not enabled on table: %', table_name;
        END IF;
        
        RAISE NOTICE 'RLS verified enabled on table: %', table_name;
    END LOOP;
END $$;

-- Log the completion
INSERT INTO activity_logs (
    company_id,
    user_id, 
    action,
    resource_type,
    resource_id,
    details,
    created_at
) VALUES (
    NULL, -- System action
    NULL, -- System action
    'rls_policies_restored',
    'security',
    'database',
    '{"message": "RLS policies restored with secure non-recursive implementation", "timestamp": "' || NOW() || '"}',
    NOW()
) ON CONFLICT DO NOTHING;

-- Success message
RAISE NOTICE 'RLS policies successfully restored with secure non-recursive implementation';
RAISE NOTICE 'All tables now have proper row-level security enabled';
RAISE NOTICE 'Company data isolation is enforced at the database level';
